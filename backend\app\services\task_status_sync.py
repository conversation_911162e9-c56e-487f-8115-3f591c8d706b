"""
任务状态同步服务
负责从Redis同步任务状态到MongoDB
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import redis.asyncio as redis
from app.core.schemas.social_repository import SocialDatabaseService

logger = logging.getLogger(__name__)


class TaskStatusSyncService:
    """任务状态同步服务"""
    
    def __init__(self, mongo_db, redis_url: str = "redis://localhost:6379"):
        self.mongo_db = mongo_db
        self.db_service = SocialDatabaseService(mongo_db)
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.sync_interval = 10  # 同步间隔（秒）
        self.running = False
        
    async def start(self):
        """启动同步服务"""
        try:
            # 连接Redis
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info("Redis连接成功")
            
            self.running = True
            logger.info("任务状态同步服务已启动")
            
            # 启动同步循环
            asyncio.create_task(self._sync_loop())
            
        except Exception as e:
            logger.error(f"启动任务状态同步服务失败: {str(e)}", exc_info=True)
            
    async def stop(self):
        """停止同步服务"""
        self.running = False
        if self.redis_client:
            await self.redis_client.close()
        logger.info("任务状态同步服务已停止")
        
    async def _sync_loop(self):
        """同步循环"""
        while self.running:
            try:
                await self._sync_task_statuses()
                await asyncio.sleep(self.sync_interval)
            except Exception as e:
                logger.error(f"同步任务状态失败: {str(e)}", exc_info=True)
                await asyncio.sleep(self.sync_interval)
                
    async def _sync_task_statuses(self):
        """同步所有任务状态"""
        try:
            if not self.redis_client:
                return
                
            # 获取所有运行中的任务
            running_tasks = list(self.db_service.db.social_tasks.find({
                "status": {"$in": ["running", "pending", "paused"]}
            }))
            
            if not running_tasks:
                return
                
            logger.debug(f"检查 {len(running_tasks)} 个运行中的任务状态")
            
            synced_count = 0
            for task in running_tasks:
                task_id = task.get("task_id")
                if not task_id:
                    continue
                    
                # 从Redis获取最新状态
                redis_status = await self._get_task_status_from_redis(task_id)
                if redis_status:
                    # 检查状态是否需要更新
                    if await self._should_update_status(task, redis_status):
                        await self._update_task_status_in_mongo(task_id, redis_status)
                        synced_count += 1
                        
            if synced_count > 0:
                logger.info(f"同步了 {synced_count} 个任务的状态")
                
        except Exception as e:
            logger.error(f"同步任务状态异常: {str(e)}", exc_info=True)
            
    async def _get_task_status_from_redis(self, task_id: str) -> Optional[Dict[str, Any]]:
        """从Redis获取任务状态"""
        try:
            key = f"task:{task_id}:latest"
            status_json = await self.redis_client.get(key)
            
            if status_json:
                return json.loads(status_json)
            return None
            
        except Exception as e:
            logger.debug(f"从Redis获取任务{task_id}状态失败: {str(e)}")
            return None
            
    async def _should_update_status(self, mongo_task: Dict[str, Any], redis_status: Dict[str, Any]) -> bool:
        """判断是否需要更新状态"""
        try:
            mongo_status = mongo_task.get("status", "")
            redis_task_status = redis_status.get("status", "")
            
            # 如果状态不同，需要更新
            if mongo_status != redis_task_status:
                return True
                
            # 如果进度不同，需要更新
            mongo_progress = mongo_task.get("progress", 0)
            redis_progress = redis_status.get("progress", 0)
            if abs(mongo_progress - redis_progress) > 1:  # 进度差异超过1%
                return True
                
            return False
            
        except Exception as e:
            logger.debug(f"判断状态更新失败: {str(e)}")
            return False
            
    async def _update_task_status_in_mongo(self, task_id: str, redis_status: Dict[str, Any]):
        """更新MongoDB中的任务状态"""
        try:
            update_data = {
                "updated_at": datetime.now()
            }
            
            # 更新状态
            if "status" in redis_status:
                update_data["status"] = redis_status["status"]
                
            # 更新进度
            if "progress" in redis_status:
                update_data["progress"] = redis_status["progress"]
                
            # 更新结束时间
            if redis_status.get("status") in ["completed", "failed", "canceled"]:
                if "end_time" in redis_status:
                    update_data["end_time"] = redis_status["end_time"]
                elif not update_data.get("end_time"):
                    update_data["end_time"] = datetime.now().isoformat()
                    
            # 更新开始时间
            if "start_time" in redis_status and redis_status["start_time"]:
                update_data["start_time"] = redis_status["start_time"]
                
            # 执行更新
            result = self.db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                logger.info(f"已同步任务{task_id}状态: {redis_status.get('status')} (进度: {redis_status.get('progress', 0)}%)")
            else:
                logger.debug(f"任务{task_id}状态无需更新")
                
        except Exception as e:
            logger.error(f"更新任务{task_id}状态到MongoDB失败: {str(e)}", exc_info=True)
            
    async def sync_single_task(self, task_id: str) -> bool:
        """同步单个任务状态"""
        try:
            if not self.redis_client:
                return False
                
            # 从Redis获取状态
            redis_status = await self._get_task_status_from_redis(task_id)
            if not redis_status:
                return False
                
            # 获取MongoDB中的任务
            mongo_task = self.db_service.db.social_tasks.find_one({"task_id": task_id})
            if not mongo_task:
                return False
                
            # 检查是否需要更新
            if await self._should_update_status(mongo_task, redis_status):
                await self._update_task_status_in_mongo(task_id, redis_status)
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"同步单个任务{task_id}状态失败: {str(e)}", exc_info=True)
            return False


# 全局同步服务实例
_sync_service: Optional[TaskStatusSyncService] = None


async def get_sync_service() -> Optional[TaskStatusSyncService]:
    """获取同步服务实例"""
    return _sync_service


async def init_sync_service(mongo_db, redis_url: str = "redis://localhost:6379"):
    """初始化同步服务"""
    global _sync_service
    try:
        _sync_service = TaskStatusSyncService(mongo_db, redis_url)
        await _sync_service.start()
        logger.info("任务状态同步服务初始化成功")
    except Exception as e:
        logger.error(f"初始化任务状态同步服务失败: {str(e)}", exc_info=True)


async def stop_sync_service():
    """停止同步服务"""
    global _sync_service
    if _sync_service:
        await _sync_service.stop()
        _sync_service = None
