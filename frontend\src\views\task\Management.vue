<template>
  <div class="task-management">
    <div class="management-header">
      <h1>🎮 任务管理</h1>
      <p class="header-description">实时管理正在运行的任务，支持启动、暂停、取消等操作</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card running">
            <div class="stat-content">
              <div class="stat-icon">🏃</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.running }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-icon">⏳</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card paused">
            <div class="stat-content">
              <div class="stat-icon">⏸️</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.paused }}</div>
                <div class="stat-label">已暂停</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="refreshTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="success" @click="startAllPendingTasks" :disabled="stats.pending === 0">
          <el-icon><VideoPlay /></el-icon>
          启动所有等待任务
        </el-button>
        <el-button type="warning" @click="pauseAllRunningTasks" :disabled="stats.running === 0">
          <el-icon><VideoPause /></el-icon>
          暂停所有运行任务
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="filterTasks">
          <el-option label="全部" value="" />
          <el-option label="运行中" value="running" />
          <el-option label="等待中" value="pending" />
          <el-option label="已暂停" value="paused" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索任务..."
          style="width: 200px; margin-left: 10px"
          @input="filterTasks"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <el-table 
        :data="filteredTasks" 
        v-loading="loading"
        stripe
        @row-click="handleRowClick"
        style="width: 100%"
      >
        <el-table-column prop="id" label="任务ID" width="200" show-overflow-tooltip />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="进度" width="150">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.progress || 0" 
              :status="getProgressStatus(row.status)"
              :stroke-width="6"
              text-inside
            />
          </template>
        </el-table-column>

        <el-table-column prop="platform_name" label="平台" width="100" />
        <el-table-column prop="account_name" label="账号" width="120" show-overflow-tooltip />
        
        <el-table-column label="任务类型" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.task_type === 'main'" type="primary" size="small">主任务</el-tag>
            <el-tag v-else-if="row.task_type === 'subtask'" type="info" size="small">子任务</el-tag>
            <el-tag v-else type="success" size="small">单任务</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                v-if="row.status === 'pending'" 
                type="success" 
                size="small" 
                @click.stop="startTask(row)"
              >
                启动
              </el-button>
              <el-button 
                v-if="row.status === 'running'" 
                type="warning" 
                size="small" 
                @click.stop="pauseTask(row)"
              >
                暂停
              </el-button>
              <el-button 
                v-if="row.status === 'paused'" 
                type="success" 
                size="small" 
                @click.stop="resumeTask(row)"
              >
                恢复
              </el-button>
              <el-button 
                v-if="['pending', 'running', 'paused'].includes(row.status)" 
                type="danger" 
                size="small" 
                @click.stop="cancelTask(row)"
              >
                取消
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                @click.stop="viewTaskDetail(row)"
              >
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="📋 任务详情" 
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="task-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ selectedTask.platform_name }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ selectedTask.account_name }}</el-descriptions-item>
          <el-descriptions-item label="设备">{{ selectedTask.device_id }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ selectedTask.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ selectedTask.content_path }}</el-descriptions-item>
        </el-descriptions>

        <!-- 实时日志 -->
        <div class="task-logs" style="margin-top: 20px;">
          <h3>📋 实时日志</h3>
          <div class="logs-container">
            <el-timeline v-if="taskLogs.length > 0">
              <el-timeline-item
                v-for="(log, index) in taskLogs"
                :key="index"
                :timestamp="formatTime(log.timestamp)"
                :type="getLogType(log.level)"
                size="small"
              >
                <span class="log-level" :class="`log-level-${log.level}`">
                  [{{ log.level.toUpperCase() }}]
                </span>
                {{ log.message }}
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无日志" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshTaskDetail">刷新</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, VideoPlay, VideoPause, Search } from '@element-plus/icons-vue'
import { getRunningTasks, startTask as apiStartTask, pauseTask as apiPauseTask, cancelTask as apiCancelTask } from '@/api/task'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const statusFilter = ref('')
const searchKeyword = ref('')
const detailDialogVisible = ref(false)
const selectedTask = ref(null)
const taskLogs = ref([])

// 统计数据
const stats = computed(() => {
  const running = tasks.value.filter(t => t.status === 'running').length
  const pending = tasks.value.filter(t => t.status === 'pending').length
  const paused = tasks.value.filter(t => t.status === 'paused').length
  const total = tasks.value.length
  
  return { running, pending, paused, total }
})

// 过滤后的任务列表
const filteredTasks = computed(() => {
  let filtered = tasks.value
  
  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(task => 
      task.id.toLowerCase().includes(keyword) ||
      task.platform_name?.toLowerCase().includes(keyword) ||
      task.account_name?.toLowerCase().includes(keyword)
    )
  }
  
  return filtered
})

// 定时器
let refreshTimer = null

// 初始化
onMounted(() => {
  fetchTasks()
  // 每5秒自动刷新
  refreshTimer = setInterval(fetchTasks, 5000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 获取运行中的任务
const fetchTasks = async () => {
  try {
    loading.value = true
    const response = await getRunningTasks()
    
    if (response && response.data) {
      tasks.value = response.data.tasks || []
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新任务
const refreshTasks = () => {
  fetchTasks()
}

// 筛选任务
const filterTasks = () => {
  // 触发计算属性重新计算
}

// 启动任务
const startTask = async (task) => {
  try {
    await apiStartTask(task.id)
    ElMessage.success('任务启动成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('启动任务失败')
  }
}

// 暂停任务
const pauseTask = async (task) => {
  try {
    await apiPauseTask(task.id)
    ElMessage.success('任务暂停成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

// 恢复任务
const resumeTask = async (task) => {
  try {
    await apiStartTask(task.id)
    ElMessage.success('任务恢复成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('恢复任务失败')
  }
}

// 取消任务
const cancelTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 ${task.id} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await apiCancelTask(task.id)
    ElMessage.success('任务取消成功')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

// 批量操作
const startAllPendingTasks = async () => {
  const pendingTasks = tasks.value.filter(t => t.status === 'pending')
  for (const task of pendingTasks) {
    try {
      await apiStartTask(task.id)
    } catch (error) {
      console.error(`启动任务 ${task.id} 失败:`, error)
    }
  }
  ElMessage.success('批量启动完成')
  await fetchTasks()
}

const pauseAllRunningTasks = async () => {
  const runningTasks = tasks.value.filter(t => t.status === 'running')
  for (const task of runningTasks) {
    try {
      await apiPauseTask(task.id)
    } catch (error) {
      console.error(`暂停任务 ${task.id} 失败:`, error)
    }
  }
  ElMessage.success('批量暂停完成')
  await fetchTasks()
}

// 查看任务详情
const viewTaskDetail = (task) => {
  selectedTask.value = task
  detailDialogVisible.value = true
  fetchTaskLogs(task.id)
}

const handleRowClick = (row) => {
  viewTaskDetail(row)
}

// 获取任务日志
const fetchTaskLogs = async (taskId) => {
  try {
    // 这里应该调用获取任务日志的API
    // const response = await getTaskLogs(taskId)
    // taskLogs.value = response.data.logs || []
    
    // 暂时使用模拟数据
    taskLogs.value = [
      {
        message: '任务开始执行',
        level: 'info',
        timestamp: new Date().toISOString()
      }
    ]
  } catch (error) {
    console.error('获取任务日志失败:', error)
    taskLogs.value = []
  }
}

const refreshTaskDetail = () => {
  if (selectedTask.value) {
    fetchTaskLogs(selectedTask.value.id)
  }
}

// 工具函数
const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'running': 'primary', 
    'paused': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '运行中',
    'paused': '已暂停', 
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const getLogType = (level) => {
  const types = {
    'success': 'success',
    'warning': 'warning', 
    'error': 'danger',
    'info': 'primary'
  }
  return types[level] || 'primary'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}
</script>

<style scoped>
.task-management {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.management-header {
  margin-bottom: 20px;
}

.management-header h1 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.8rem;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 2rem;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.task-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.task-detail {
  max-height: 500px;
  overflow-y: auto;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-level {
  font-weight: bold;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 8px;
}

.log-level-info {
  background-color: #e1f5fe;
  color: #0277bd;
}

.log-level-success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.log-level-warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.log-level-error {
  background-color: #ffebee;
  color: #c62828;
}
</style>
