<template>
  <div class="task-management">
    <div class="management-header">
      <h1>🎮 任务管理</h1>
      <p class="header-description">实时管理正在运行的任务，支持启动、暂停、取消等操作</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card running">
            <div class="stat-content">
              <div class="stat-icon">🏃</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.running }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-icon">⏳</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">等待中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card paused">
            <div class="stat-content">
              <div class="stat-icon">⏸️</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.paused }}</div>
                <div class="stat-label">已暂停</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="refreshTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-switch
          v-model="autoRefresh"
          @change="toggleAutoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          style="margin-left: 10px;"
        />
        <el-button type="success" @click="startAllPendingTasks" :disabled="stats.pending === 0">
          <el-icon><VideoPlay /></el-icon>
          启动所有等待任务
        </el-button>
        <el-button type="warning" @click="pauseAllRunningTasks" :disabled="stats.running === 0">
          <el-icon><VideoPause /></el-icon>
          暂停所有运行任务
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="filterTasks">
          <el-option label="全部" value="" />
          <el-option label="运行中" value="running" />
          <el-option label="等待中" value="pending" />
          <el-option label="已暂停" value="paused" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索任务..."
          style="width: 200px; margin-left: 10px"
          @input="filterTasks"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <el-table
        :data="treeData"
        v-loading="loading"
        stripe
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :expand-row-keys="expandedRows"
        @row-click="handleRowClick"
        style="width: 100%"
      >
        <el-table-column label="任务信息" width="300">
          <template #default="{ row }">
            <div class="task-info">
              <div class="task-id">
                <span class="id-text">{{ row.id }}</span>
                <el-tag
                  :type="getTaskTypeColor(row.task_type)"
                  size="small"
                  style="margin-left: 8px;"
                >
                  {{ getTaskTypeText(row.task_type) }}
                </el-tag>
              </div>
              <div v-if="row.task_type === 'main'" class="subtask-info">
                {{ row.completed_subtasks || 0 }}/{{ row.total_subtasks || 0 }} 子任务
              </div>
              <div v-if="row.task_type === 'subtask'" class="video-file">
                📹 {{ getFileName(row.video_file) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="进度" width="150">
          <template #default="{ row }">
            <el-progress
              :percentage="getTaskProgress(row)"
              :status="getProgressStatus(row.status)"
              :stroke-width="6"
              text-inside
            />
          </template>
        </el-table-column>

        <el-table-column prop="platform_name" label="平台" width="100" />
        <el-table-column prop="account_name" label="账号" width="120" show-overflow-tooltip />

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="row.status === 'pending'"
                type="success"
                size="small"
                @click.stop="startTask(row)"
              >
                启动
              </el-button>
              <el-button
                v-if="row.status === 'running'"
                type="warning"
                size="small"
                @click.stop="pauseTask(row)"
              >
                暂停
              </el-button>
              <el-button
                v-if="row.status === 'paused'"
                type="success"
                size="small"
                @click.stop="resumeTask(row)"
              >
                恢复
              </el-button>
              <el-button
                v-if="['pending', 'running', 'paused'].includes(row.status)"
                type="danger"
                size="small"
                @click.stop="cancelTask(row)"
              >
                取消
              </el-button>
              <el-button
                v-if="['completed', 'failed', 'canceled'].includes(row.status)"
                type="danger"
                size="small"
                @click.stop="deleteTask(row)"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click.stop="viewTaskDetail(row)"
              >
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      title="📋 任务详情" 
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="task-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ selectedTask.platform_name }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ selectedTask.account_name }}</el-descriptions-item>
          <el-descriptions-item label="设备">{{ selectedTask.device_id }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ selectedTask.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ selectedTask.content_path }}</el-descriptions-item>
        </el-descriptions>

        <!-- 实时日志 -->
        <div class="task-logs" style="margin-top: 20px;">
          <h3>📋 实时日志</h3>
          <div class="logs-container">
            <el-timeline v-if="taskLogs.length > 0">
              <el-timeline-item
                v-for="(log, index) in taskLogs"
                :key="index"
                :timestamp="formatTime(log.timestamp)"
                :type="getLogType(log.level)"
                size="small"
              >
                <span class="log-level" :class="`log-level-${log.level}`">
                  [{{ log.level.toUpperCase() }}]
                </span>
                {{ log.message }}
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无日志" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshTaskDetail">刷新</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, VideoPlay, VideoPause, Search } from '@element-plus/icons-vue'
import { getRunningTasks, startTask as apiStartTask, pauseTask as apiPauseTask, cancelTask as apiCancelTask, deleteTask as deleteTaskApi } from '@/api/task'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const statusFilter = ref('')
const searchKeyword = ref('')
const detailDialogVisible = ref(false)
const selectedTask = ref(null)
const taskLogs = ref([])
const autoRefresh = ref(false) // 默认关闭自动刷新
const expandedRows = ref([]) // 展开的行

// 统计数据
const stats = computed(() => {
  const running = tasks.value.filter(t => t.status === 'running').length
  const pending = tasks.value.filter(t => t.status === 'pending').length
  const paused = tasks.value.filter(t => t.status === 'paused').length
  const total = tasks.value.length
  
  return { running, pending, paused, total }
})

// 过滤后的任务列表
const filteredTasks = computed(() => {
  let filtered = tasks.value

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(task =>
      task.id.toLowerCase().includes(keyword) ||
      task.platform_name?.toLowerCase().includes(keyword) ||
      task.account_name?.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 树形数据结构
const treeData = computed(() => {
  const filtered = filteredTasks.value
  const mainTasks = filtered.filter(task => task.task_type === 'main')
  const singleTasks = filtered.filter(task => task.task_type === 'single' || !task.task_type)
  const subtasks = filtered.filter(task => task.task_type === 'subtask')

  // 构建树形结构
  const treeNodes = []

  // 添加主任务及其子任务
  mainTasks.forEach(mainTask => {
    const children = subtasks
      .filter(subtask => subtask.parent_task_id === mainTask.id)
      .sort((a, b) => (a.subtask_index || 0) - (b.subtask_index || 0))

    treeNodes.push({
      ...mainTask,
      children: children,
      hasChildren: children.length > 0
    })

    // 自动展开有运行中子任务的主任务
    if (children.some(child => child.status === 'running')) {
      if (!expandedRows.value.includes(mainTask.id)) {
        expandedRows.value.push(mainTask.id)
      }
    }
  })

  // 添加单任务
  singleTasks.forEach(singleTask => {
    treeNodes.push({
      ...singleTask,
      children: [],
      hasChildren: false
    })
  })

  return treeNodes
})

// 定时器
let refreshTimer = null

// 初始化
onMounted(() => {
  fetchTasks()
  // 默认不启动自动刷新，由用户手动控制
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

// 切换自动刷新
const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    // 启动自动刷新
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }
    refreshTimer = setInterval(fetchTasks, 30000) // 30秒间隔
    ElMessage.success('已启用自动刷新（30秒间隔）')
  } else {
    // 停止自动刷新
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已关闭自动刷新')
  }
}

// 获取运行中的任务
const fetchTasks = async () => {
  // 如果正在加载中，跳过这次刷新
  if (loading.value) {
    return
  }

  try {
    loading.value = true
    const response = await getRunningTasks()

    if (response && response.data) {
      tasks.value = response.data.tasks || []
    } else {
      console.warn('API响应格式异常:', response)
      // 不显示错误消息，避免频繁弹窗
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    // 只在手动刷新时显示错误消息
    // ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新任务（手动刷新，显示错误消息）
const refreshTasks = async () => {
  try {
    loading.value = true
    const response = await getRunningTasks()

    if (response && response.data) {
      tasks.value = response.data.tasks || []
      ElMessage.success('刷新成功')
    } else {
      console.warn('API响应格式异常:', response)
      ElMessage.warning('数据格式异常')
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 筛选任务
const filterTasks = () => {
  // 触发计算属性重新计算
}

// 启动任务
const startTask = async (task) => {
  try {
    await apiStartTask(task.id)
    ElMessage.success('任务启动成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('启动任务失败')
  }
}

// 暂停任务
const pauseTask = async (task) => {
  try {
    await apiPauseTask(task.id)
    ElMessage.success('任务暂停成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

// 恢复任务
const resumeTask = async (task) => {
  try {
    await apiStartTask(task.id)
    ElMessage.success('任务恢复成功')
    await fetchTasks()
  } catch (error) {
    ElMessage.error('恢复任务失败')
  }
}

// 取消任务
const cancelTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 ${task.id} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await apiCancelTask(task.id)
    ElMessage.success('任务取消成功')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

// 删除任务
const deleteTask = async (task) => {
  try {
    const taskTypeText = getTaskTypeText(task.task_type)
    let confirmMessage = `确定要删除${taskTypeText} ${task.id} 吗？`

    // 如果是主任务，提醒会同时删除子任务
    if (task.task_type === 'main') {
      confirmMessage += '\n注意：删除主任务会同时删除所有子任务！'
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    // 调用删除API
    await deleteTaskApi(task.id)
    ElMessage.success('任务删除成功')
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

// 批量操作
const startAllPendingTasks = async () => {
  const pendingTasks = tasks.value.filter(t => t.status === 'pending')
  for (const task of pendingTasks) {
    try {
      await apiStartTask(task.id)
    } catch (error) {
      console.error(`启动任务 ${task.id} 失败:`, error)
    }
  }
  ElMessage.success('批量启动完成')
  await fetchTasks()
}

const pauseAllRunningTasks = async () => {
  const runningTasks = tasks.value.filter(t => t.status === 'running')
  for (const task of runningTasks) {
    try {
      await apiPauseTask(task.id)
    } catch (error) {
      console.error(`暂停任务 ${task.id} 失败:`, error)
    }
  }
  ElMessage.success('批量暂停完成')
  await fetchTasks()
}

// 查看任务详情
const viewTaskDetail = (task) => {
  selectedTask.value = task
  detailDialogVisible.value = true
  fetchTaskLogs(task.id)
}

const handleRowClick = (row) => {
  viewTaskDetail(row)
}

// 获取任务日志
const fetchTaskLogs = async (taskId) => {
  try {
    // 这里应该调用获取任务日志的API
    // const response = await getTaskLogs(taskId)
    // taskLogs.value = response.data.logs || []
    
    // 暂时使用模拟数据
    taskLogs.value = [
      {
        message: '任务开始执行',
        level: 'info',
        timestamp: new Date().toISOString()
      }
    ]
  } catch (error) {
    console.error('获取任务日志失败:', error)
    taskLogs.value = []
  }
}

const refreshTaskDetail = () => {
  if (selectedTask.value) {
    fetchTaskLogs(selectedTask.value.id)
  }
}

// 工具函数
const getStatusType = (status) => {
  const types = {
    'pending': 'info',
    'running': 'primary', 
    'paused': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '等待中',
    'running': '运行中',
    'paused': '已暂停', 
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const getLogType = (level) => {
  const types = {
    'success': 'success',
    'warning': 'warning', 
    'error': 'danger',
    'info': 'primary'
  }
  return types[level] || 'primary'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

// 任务类型相关函数
const getTaskTypeText = (taskType) => {
  const texts = {
    'main': '主任务',
    'subtask': '子任务',
    'single': '单任务'
  }
  return texts[taskType] || '单任务'
}

const getTaskTypeColor = (taskType) => {
  const colors = {
    'main': 'primary',
    'subtask': 'info',
    'single': 'success'
  }
  return colors[taskType] || 'success'
}

// 获取任务进度
const getTaskProgress = (task) => {
  if (task.task_type === 'main') {
    // 主任务进度 = 已完成子任务数 / 总子任务数 * 100
    const completed = task.completed_subtasks || 0
    const total = task.total_subtasks || 1
    return Math.round((completed / total) * 100)
  }
  return task.progress || 0
}

// 获取文件名
const getFileName = (filePath) => {
  if (!filePath) return ''
  return filePath.split(/[/\\]/).pop() || filePath
}
</script>

<style scoped>
.task-management {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.management-header {
  margin-bottom: 20px;
}

.management-header h1 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.8rem;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 2rem;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.task-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.task-detail {
  max-height: 500px;
  overflow-y: auto;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-level {
  font-weight: bold;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 8px;
}

.log-level-info {
  background-color: #e1f5fe;
  color: #0277bd;
}

.log-level-success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.log-level-warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.log-level-error {
  background-color: #ffebee;
  color: #c62828;
}

/* 任务信息样式 */
.task-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-id {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.id-text {
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.subtask-info {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.video-file {
  font-size: 12px;
  color: #67c23a;
  font-weight: normal;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 树形表格样式优化 */
:deep(.el-table__expand-icon) {
  color: #409EFF;
}

:deep(.el-table__row--level-1) {
  background-color: #f8f9fa;
}

:deep(.el-table__row--level-1 .task-info) {
  padding-left: 20px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}
</style>
