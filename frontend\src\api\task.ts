import request from '@/utils/request'

// 任务相关接口

// 获取任务列表
export const getTaskList = (params?: {
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}) => {
  return request({
    url: '/api/tasks/',
    method: 'get',
    params
  })
}

// 获取任务详情
export const getTaskDetail = (taskId: string) => {
  return request({
    url: `/api/tasks/detail/${taskId}`,
    method: 'get'
  })
}

// 获取任务状态
export const getTaskStatus = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/status`,
    method: 'get'
  })
}

// 获取任务日志
export const getTaskLogs = (taskId: string) => {
  return request({
    url: `/api/tasks/detail/${taskId}/logs`,
    method: 'get'
  })
}

// 获取运行中的任务
export const getRunningTasks = () => {
  return request({
    url: '/api/tasks/running',
    method: 'get'
  })
}

// 启动任务
export const startTask = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/start`,
    method: 'post'
  })
}

// 暂停任务
export const pauseTask = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/pause`,
    method: 'post'
  })
}

// 取消任务
export const cancelTask = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/cancel`,
    method: 'post'
  })
}

// 获取任务结果
export const getTaskResult = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}/result`,
    method: 'get'
  })
}

// 删除任务
export const deleteTask = (taskId: string) => {
  return request({
    url: `/api/tasks/${taskId}`,
    method: 'delete'
  })
}

// 批量操作任务
export const batchOperateTasks = (taskIds: string[], operation: string) => {
  return request({
    url: '/api/tasks/batch',
    method: 'post',
    data: {
      task_ids: taskIds,
      operation
    }
  })
}

// 获取任务统计
export const getTaskStats = () => {
  return request({
    url: '/api/tasks/stats',
    method: 'get'
  })
}

// 获取任务历史
export const getTaskHistory = (params?: {
  start_date?: string
  end_date?: string
  status?: string
  platform_id?: string
  limit?: number
  offset?: number
}) => {
  return request({
    url: '/api/tasks/history',
    method: 'get',
    params
  })
}

// 清理任务历史
export const cleanTaskHistory = (params: {
  before_date?: string
  status?: string
  keep_count?: number
  clean_invalid?: boolean  // 新增：清理无效任务
}) => {
  return request({
    url: '/api/tasks/history/clean',
    method: 'post',
    data: params
  })
}

// 导出任务数据
export const exportTasks = (params?: {
  start_date?: string
  end_date?: string
  status?: string
  format?: 'csv' | 'excel'
}) => {
  return request({
    url: '/api/tasks/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
