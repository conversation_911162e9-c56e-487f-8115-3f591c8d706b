"""
任务管理API
独立的任务调度和管理接口
"""

import logging
import datetime
from fastapi import APIRouter, Request, Query
from typing import Optional
from bson import ObjectId
from app.core.schemas.social_repository import SocialDatabaseService

logger = logging.getLogger(__name__)

def init_task_routes():
    """初始化任务管理路由"""
    router = APIRouter(
        prefix="/api/tasks",
        tags=["tasks"],
        responses={404: {"description": "Not found"}},
    )

    @router.get("/")
    async def get_tasks():
        """获取任务列表"""
        return {"message": "任务管理API开发中", "tasks": []}

    @router.get("/stats")
    async def get_task_stats():
        """获取任务统计"""
        return {
            "status_stats": {
                "running": 0,
                "pending": 0,
                "completed": 0,
                "failed": 0
            },
            "message": "任务统计API开发中"
        }

    @router.get("/detail/{task_id}")
    async def get_task_detail(
        task_id: str,
        request: Request
    ):
        """获取任务详情"""
        try:
            logger.info(f"获取任务详情: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 查询任务
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {
                    "task": None,
                    "error": "任务不存在"
                }

            # 移除MongoDB的_id字段
            if '_id' in task:
                del task['_id']

            # 获取平台信息
            platform_name = "未知平台"
            platform_id = task.get("platform_id", "")
            if platform_id:
                try:
                    if isinstance(platform_id, str) and len(platform_id) == 24:
                        platform = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                    else:
                        platform = db_service.db.social_platforms.find_one({"_id": platform_id})

                    if platform:
                        platform_name = platform.get("name", platform.get("id", "未知平台"))
                except Exception as e:
                    logger.warning(f"获取平台信息失败: {e}")

            # 获取账号信息
            account_name = "未知账号"
            account_id = task.get("account_id", "")
            if account_id:
                try:
                    if isinstance(account_id, str) and len(account_id) == 24:
                        account = db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                    else:
                        account = db_service.db.social_accounts.find_one({"_id": account_id})

                    if account:
                        account_name = account.get("display_name", account.get("username", "未知账号"))
                except Exception as e:
                    logger.warning(f"获取账号信息失败: {e}")

            # 格式化任务数据
            formatted_task = {
                "id": task.get("task_id", task.get("id", "")),
                "platform_id": platform_id,
                "platform_name": platform_name,
                "account_id": account_id,
                "account_name": account_name,
                "device_id": task.get("device_id", ""),
                "content_path": task.get("content_path", ""),
                "status": task.get("status", "unknown"),
                "progress": task.get("progress", 100 if task.get("status") == "completed" else 0),
                "created_at": task.get("created_at", ""),
                "updated_at": task.get("updated_at", ""),
                "start_time": task.get("start_time", ""),
                "end_time": task.get("end_time", ""),
                "estimated_end_time": task.get("estimated_end_time", ""),
                "workflow_id": task.get("workflow_id", ""),
                "params": task.get("params", {})
            }

            logger.info(f"找到任务: {formatted_task['id']}, 状态: {formatted_task['status']}")

            return {
                "task": formatted_task
            }

        except Exception as e:
            logger.error(f"获取任务详情失败: {str(e)}", exc_info=True)
            return {
                "task": None,
                "error": f"获取任务详情失败: {str(e)}"
            }

    @router.get("/detail/{task_id}/logs")
    async def get_task_logs_detail(
        task_id: str,
        request: Request,
        limit: int = Query(100, description="日志数量限制"),
        offset: int = Query(0, description="偏移量")
    ):
        """获取任务日志"""
        try:
            logger.info(f"获取任务日志: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {
                    "logs": [],
                    "total": 0,
                    "error": "任务不存在"
                }

            # 从数据库获取任务日志
            logs = list(db_service.db.social_task_logs.find(
                {"task_id": task_id}
            ).sort("created_at", 1).skip(offset).limit(limit))

            # 格式化日志
            formatted_logs = []
            for log in logs:
                if '_id' in log:
                    del log['_id']
                formatted_logs.append({
                    "message": log.get("message", ""),
                    "level": log.get("level", "info"),
                    "timestamp": log.get("timestamp", log.get("created_at", "")),
                    "created_at": log.get("created_at", "")
                })

            # 获取总日志数
            total_logs = db_service.db.social_task_logs.count_documents({"task_id": task_id})

            logger.info(f"找到 {len(formatted_logs)} 条日志，总计 {total_logs} 条")

            return {
                "logs": formatted_logs,
                "total": total_logs,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": total_logs > offset + len(formatted_logs)
                }
            }

        except Exception as e:
            logger.error(f"获取任务日志失败: {str(e)}", exc_info=True)
            return {
                "logs": [],
                "total": 0,
                "error": f"获取任务日志失败: {str(e)}"
            }

    @router.get("/running")
    async def get_running_tasks(request: Request):
        """获取运行中的任务"""
        try:
            logger.info("获取运行中的任务")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 查询运行中、等待中、暂停中的任务
            query = {
                "status": {"$in": ["running", "pending", "paused"]}
            }

            # 查询任务
            tasks = list(db_service.db.social_tasks.find(query).sort("created_at", -1).limit(100))

            # 格式化任务数据
            formatted_tasks = []
            for task in tasks:
                if '_id' in task:
                    del task['_id']

                # 获取平台信息
                platform_name = "未知平台"
                platform_id = task.get("platform_id", "")
                if platform_id:
                    try:
                        if isinstance(platform_id, str) and len(platform_id) == 24:
                            platform = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                        else:
                            platform = db_service.db.social_platforms.find_one({"_id": platform_id})

                        if platform:
                            platform_name = platform.get("name", platform.get("id", "未知平台"))
                    except Exception as e:
                        logger.warning(f"获取平台信息失败: {e}")

                # 获取账号信息
                account_name = "未知账号"
                account_id = task.get("account_id", "")
                if account_id:
                    try:
                        if isinstance(account_id, str) and len(account_id) == 24:
                            account = db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                        else:
                            account = db_service.db.social_accounts.find_one({"_id": account_id})

                        if account:
                            account_name = account.get("display_name", account.get("username", "未知账号"))
                    except Exception as e:
                        logger.warning(f"获取账号信息失败: {e}")

                formatted_task = {
                    "id": task.get("task_id", task.get("id", "")),
                    "platform_id": platform_id,
                    "platform_name": platform_name,
                    "account_id": account_id,
                    "account_name": account_name,
                    "device_id": task.get("device_id", ""),
                    "content_path": task.get("content_path", ""),
                    "status": task.get("status", "unknown"),
                    "progress": task.get("progress", 0),
                    "task_type": task.get("task_type", "single"),
                    "created_at": task.get("created_at", ""),
                    "updated_at": task.get("updated_at", ""),
                    "start_time": task.get("start_time", ""),
                    "workflow_id": task.get("workflow_id", ""),
                    "workflow_name": task.get("workflow_name", ""),  # 新增工作流名称
                    "content_type": task.get("content_type", ""),    # 新增内容类型
                    "video_file": task.get("video_file", "")         # 新增视频文件
                }

                # 如果是主任务，添加子任务信息
                if task.get("task_type") == "main":
                    formatted_task["total_subtasks"] = task.get("total_subtasks", 0)
                    formatted_task["completed_subtasks"] = task.get("completed_subtasks", 0)

                # 如果是子任务，添加父任务信息
                if task.get("task_type") == "subtask":
                    formatted_task["parent_task_id"] = task.get("parent_task_id", "")
                    formatted_task["subtask_index"] = task.get("subtask_index", 1)

                formatted_tasks.append(formatted_task)

            logger.info(f"找到 {len(formatted_tasks)} 个运行中的任务")

            return {
                "tasks": formatted_tasks,
                "total": len(formatted_tasks)
            }

        except Exception as e:
            logger.error(f"获取运行中任务失败: {str(e)}", exc_info=True)
            return {
                "tasks": [],
                "total": 0,
                "error": f"获取运行中任务失败: {str(e)}"
            }

    @router.post("/{task_id}/start")
    async def start_task(task_id: str, request: Request):
        """启动任务"""
        try:
            logger.info(f"启动任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态
            if task.get("status") not in ["pending", "paused"]:
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法启动"}

            # 更新任务状态
            db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {"status": "running", "updated_at": datetime.datetime.now()}}
            )

            # 这里应该通知Core服务启动任务
            # 暂时只更新数据库状态

            logger.info(f"任务 {task_id} 启动成功")
            return {"success": True, "message": "任务启动成功"}

        except Exception as e:
            logger.error(f"启动任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"启动任务失败: {str(e)}"}

    @router.post("/{task_id}/pause")
    async def pause_task(task_id: str, request: Request):
        """暂停任务"""
        try:
            logger.info(f"暂停任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态
            if task.get("status") != "running":
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法暂停"}

            # 更新任务状态
            db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {"status": "paused", "updated_at": datetime.datetime.now()}}
            )

            # 这里应该通知Core服务暂停任务
            # 暂时只更新数据库状态

            logger.info(f"任务 {task_id} 暂停成功")
            return {"success": True, "message": "任务暂停成功"}

        except Exception as e:
            logger.error(f"暂停任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"暂停任务失败: {str(e)}"}

    @router.post("/{task_id}/cancel")
    async def cancel_task(task_id: str, request: Request):
        """取消任务"""
        try:
            logger.info(f"取消任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态
            if task.get("status") not in ["pending", "running", "paused"]:
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法取消"}

            # 更新任务状态
            db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {
                    "status": "canceled",
                    "end_time": datetime.datetime.now().isoformat(),
                    "updated_at": datetime.datetime.now()
                }}
            )

            # 这里应该通知Core服务取消任务
            # 暂时只更新数据库状态

            logger.info(f"任务 {task_id} 取消成功")
            return {"success": True, "message": "任务取消成功"}

        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"取消任务失败: {str(e)}"}

    @router.delete("/{task_id}")
    async def delete_task(task_id: str, request: Request):
        """删除任务"""
        try:
            logger.info(f"删除任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态，只能删除已完成、失败或取消的任务
            if task.get("status") not in ["completed", "failed", "canceled"]:
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法删除。只能删除已完成、失败或已取消的任务"}

            # 如果是主任务，需要同时删除所有子任务
            if task.get("task_type") == "main":
                logger.info(f"删除主任务 {task_id}，同时删除所有子任务")

                # 查找所有子任务
                subtasks = list(db_service.db.social_tasks.find({"parent_task_id": task_id}))
                logger.info(f"找到 {len(subtasks)} 个子任务")

                # 删除所有子任务
                if subtasks:
                    subtask_result = db_service.db.social_tasks.delete_many({"parent_task_id": task_id})
                    logger.info(f"删除了 {subtask_result.deleted_count} 个子任务")

            # 删除主任务或单任务
            result = db_service.db.social_tasks.delete_one({"task_id": task_id})

            if result.deleted_count == 0:
                return {"success": False, "error": "删除任务失败"}

            logger.info(f"任务 {task_id} 删除成功")
            return {"success": True, "message": "任务删除成功"}

        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"删除任务失败: {str(e)}"}

    @router.get("/history")
    async def get_task_history(
        request: Request,
        status: Optional[str] = Query(None, description="任务状态过滤"),
        platform_id: Optional[str] = Query(None, description="平台ID过滤"),
        start_date: Optional[str] = Query(None, description="开始日期"),
        end_date: Optional[str] = Query(None, description="结束日期"),
        limit: int = Query(20, description="返回数量限制"),
        offset: int = Query(0, description="偏移量")
    ):
        """获取任务历史"""
        try:
            logger.info(f"获取任务历史: status={status}, platform_id={platform_id}, limit={limit}, offset={offset}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 构建查询条件
            query = {}

            if status:
                query["status"] = status
            if platform_id:
                query["platform_id"] = platform_id
            if start_date:
                query["created_at"] = {"$gte": start_date}
            if end_date:
                if "created_at" in query:
                    query["created_at"]["$lte"] = end_date
                else:
                    query["created_at"] = {"$lte": end_date}

            logger.info(f"查询条件: {query}")

            # 从数据库获取任务历史
            tasks_cursor = db_service.db.social_tasks.find(query).sort("created_at", -1).skip(offset).limit(limit)
            tasks = list(tasks_cursor)

            # 格式化任务数据
            formatted_tasks = []
            for task in tasks:
                # 移除MongoDB的_id字段
                if '_id' in task:
                    del task['_id']

                # 获取平台信息
                platform_name = "未知平台"
                platform_id = task.get("platform_id", "")
                if platform_id:
                    try:
                        # 尝试使用ObjectId查询
                        if isinstance(platform_id, str) and len(platform_id) == 24:
                            platform = db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                        else:
                            platform = db_service.db.social_platforms.find_one({"_id": platform_id})

                        if platform:
                            platform_name = platform.get("name", platform.get("id", "未知平台"))
                            logger.debug(f"找到平台: {platform_name}")
                        else:
                            logger.warning(f"未找到平台: {platform_id}")
                    except Exception as e:
                        logger.warning(f"获取平台信息失败: {e}")

                # 获取账号信息
                account_name = "未知账号"
                account_id = task.get("account_id", "")
                if account_id:
                    try:
                        # 尝试使用ObjectId查询
                        if isinstance(account_id, str) and len(account_id) == 24:
                            account = db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                        else:
                            account = db_service.db.social_accounts.find_one({"_id": account_id})

                        if account:
                            account_name = account.get("display_name", account.get("username", "未知账号"))
                            logger.debug(f"找到账号: {account_name}")
                        else:
                            logger.warning(f"未找到账号: {account_id}")
                    except Exception as e:
                        logger.warning(f"获取账号信息失败: {e}")

                # 确保必要字段存在
                formatted_task = {
                    "id": task.get("task_id", task.get("id", "")),  # 优先使用task_id
                    "platform_id": platform_id,
                    "platform_name": platform_name,  # 新增平台名称
                    "account_id": account_id,
                    "account_name": account_name,  # 新增账号名称
                    "device_id": task.get("device_id", ""),
                    "content_path": task.get("content_path", ""),
                    "status": task.get("status", "unknown"),
                    "progress": task.get("progress", 100 if task.get("status") == "completed" else 0),  # 已完成任务进度为100%
                    "created_at": task.get("created_at", ""),
                    "updated_at": task.get("updated_at", ""),
                    "start_time": task.get("start_time", ""),
                    "end_time": task.get("end_time", ""),
                    "estimated_end_time": task.get("estimated_end_time", ""),
                    "workflow_id": task.get("workflow_id", ""),
                    "workflow_name": task.get("workflow_name", ""),  # 新增工作流名称
                    "content_type": task.get("content_type", ""),    # 新增内容类型
                    "params": task.get("params", {})
                }
                formatted_tasks.append(formatted_task)

            # 获取总数
            total_count = db_service.db.social_tasks.count_documents(query)

            logger.info(f"找到 {len(formatted_tasks)} 条历史记录，总计 {total_count} 条")

            return {
                "tasks": formatted_tasks,
                "total": total_count,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": total_count > offset + len(formatted_tasks)
                }
            }

        except Exception as e:
            logger.error(f"获取任务历史失败: {str(e)}", exc_info=True)
            return {
                "tasks": [],
                "total": 0,
                "error": f"获取任务历史失败: {str(e)}"
            }

    @router.post("/history/clean")
    async def clean_task_history(request: Request):
        """清理任务历史"""
        try:
            # 获取请求数据
            clean_data = await request.json()
            logger.info(f"清理任务历史: {clean_data}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 构建删除条件
            delete_query = {}

            # 按日期清理
            if "before_date" in clean_data and clean_data["before_date"]:
                before_date = clean_data["before_date"]
                # 将日期字符串转换为datetime对象
                try:
                    import datetime as dt
                    date_obj = dt.datetime.strptime(before_date, "%Y-%m-%d")
                    delete_query["created_at"] = {"$lt": date_obj}
                    logger.info(f"按日期清理: 删除 {before_date} 之前的任务")
                except ValueError as e:
                    logger.error(f"日期格式错误: {before_date}, {e}")
                    return {"success": False, "error": "日期格式错误"}

            # 按状态清理
            elif "status" in clean_data and clean_data["status"]:
                status = clean_data["status"]
                delete_query["status"] = status
                logger.info(f"按状态清理: 删除状态为 {status} 的任务")

            # 按保留数量清理（智能保留，维护任务关系）
            elif "keep_count" in clean_data and clean_data["keep_count"]:
                keep_count = int(clean_data["keep_count"])
                logger.info(f"按保留数量清理: 保留最新 {keep_count} 个任务组")

                # 智能任务保留逻辑：
                # 1. 主任务和单任务按创建时间排序
                # 2. 保留最新的N个任务组（主任务+子任务算作一组，单任务算作一组）
                # 3. 确保不会出现孤儿子任务

                # 获取所有主任务和单任务，按创建时间倒序排列
                main_and_single_tasks = list(db_service.db.social_tasks.find({
                    "$or": [
                        {"task_type": "main"},
                        {"task_type": "single"},
                        {"task_type": {"$exists": False}}  # 兼容没有task_type的旧任务
                    ]
                }).sort("created_at", -1))

                logger.info(f"找到 {len(main_and_single_tasks)} 个任务组（主任务+单任务）")

                if len(main_and_single_tasks) > keep_count:
                    # 需要删除的任务组
                    tasks_to_delete = main_and_single_tasks[keep_count:]

                    # 收集要删除的任务ID
                    task_ids_to_delete = []

                    for task in tasks_to_delete:
                        task_id = task.get("task_id")
                        if task_id:
                            task_ids_to_delete.append(task_id)

                            # 如果是主任务，还要删除所有子任务
                            if task.get("task_type") == "main":
                                subtasks = list(db_service.db.social_tasks.find({"parent_task_id": task_id}))
                                for subtask in subtasks:
                                    subtask_id = subtask.get("task_id")
                                    if subtask_id:
                                        task_ids_to_delete.append(subtask_id)
                                logger.info(f"主任务 {task_id} 包含 {len(subtasks)} 个子任务，将一并删除")

                    if task_ids_to_delete:
                        delete_query = {"task_id": {"$in": task_ids_to_delete}}
                        logger.info(f"将删除 {len(task_ids_to_delete)} 个任务（包括子任务）")
                    else:
                        logger.info("没有需要删除的任务")
                        return {"success": True, "deleted_count": 0, "message": "没有需要删除的任务"}
                else:
                    logger.info(f"当前任务组数量 {len(main_and_single_tasks)} 不超过保留数量 {keep_count}")
                    return {"success": True, "deleted_count": 0, "message": "当前任务组数量不超过保留数量"}

            # 清理无效任务（没有task_id的记录和孤儿子任务）
            elif "clean_invalid" in clean_data and clean_data["clean_invalid"]:
                logger.info("清理无效任务: 删除没有task_id的记录和孤儿子任务")

                # 首先找出所有孤儿子任务
                pipeline = [
                    {"$match": {"task_type": "subtask", "parent_task_id": {"$exists": True}}},
                    {"$lookup": {
                        "from": "social_tasks",
                        "localField": "parent_task_id",
                        "foreignField": "task_id",
                        "as": "parent"
                    }},
                    {"$match": {"parent": {"$size": 0}}},
                    {"$project": {"task_id": 1}}
                ]

                orphan_subtasks = list(db_service.db.social_tasks.aggregate(pipeline))
                orphan_task_ids = [task["task_id"] for task in orphan_subtasks]

                logger.info(f"找到 {len(orphan_task_ids)} 个孤儿子任务: {orphan_task_ids}")

                # 构建删除查询：无效任务 + 孤儿子任务
                delete_conditions = [
                    {"task_id": {"$exists": False}},
                    {"task_id": None},
                    {"task_id": ""}
                ]

                if orphan_task_ids:
                    delete_conditions.append({"task_id": {"$in": orphan_task_ids}})

                delete_query = {"$or": delete_conditions}

            else:
                return {"success": False, "error": "请指定清理条件"}

            # 执行删除操作
            if delete_query:
                logger.info(f"执行删除操作，查询条件: {delete_query}")
                result = db_service.db.social_tasks.delete_many(delete_query)
                deleted_count = result.deleted_count

                logger.info(f"清理完成，删除了 {deleted_count} 个任务")
                return {
                    "success": True,
                    "deleted_count": deleted_count,
                    "message": f"成功清理了 {deleted_count} 个历史任务"
                }
            else:
                return {"success": False, "error": "没有匹配的清理条件"}

        except Exception as e:
            logger.error(f"清理任务历史失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"清理任务历史失败: {str(e)}"
            }

    return router
