import os
import json
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.encoders import jsonable_encoder
from typing import List, Dict, Any, Optional
import asyncio
import datetime
import logging
import uuid
from app.core.schemas.social_repository import SocialDatabaseService
from app.core.security import get_current_user
from app.core.client import TaskServiceClient
from app.services.consul_discovery import ConsulDiscovery

def get_social_service(request: Request):
    """获取社媒管理服务实例"""
    from app.core.schemas.social_repository import SocialDatabaseService
    db = request.app.state.mongo_db
    service = SocialDatabaseService(db)
    logger = logging.getLogger(__name__)
    logger.debug("社媒管理服务实例已创建")
    return service

def init_social_routes(fastapi_app, sio):
    router = APIRouter(
        prefix="/api/social",
        tags=["social"],
        dependencies=[Depends(get_current_user)]
    )

    @router.get("/apps", response_model=List[dict])
    async def get_social_apps(
        request: Request,
        skip: int = 0,
        limit: int = 100,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取社媒应用列表"""
        logger = logging.getLogger(__name__)
        try:
            logger.info("Received request for social apps")
            apps = db_service.get_social_apps(skip=skip, limit=limit)
            logger.info(f"Returning {len(apps)} social apps")
            logger.debug(f"App data: {apps}")
            return apps
        except Exception as e:
            logger.error(f"Failed to get social apps: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="获取社媒应用列表失败")

    @router.get("/accounts", response_model=List[dict])
    async def get_social_accounts(
        request: Request,
        app_id: str = None,
        skip: int = 0,
        limit: int = 100,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取社媒账号列表
        app_id: 可选参数，为空时返回所有账号
        """
        logger = logging.getLogger(__name__)
        try:
            logger.info(f"Fetching accounts with app_id: {app_id}")
            accounts = list(db_service.get_accounts(app_id, skip=skip, limit=limit))
            logger.info(f"Found {len(accounts)} accounts")
            logger.debug(f"Raw accounts data: {accounts}")

            # 转换ObjectId为字符串
            formatted_accounts = []
            for account in accounts:
                if '_id' in account:
                    account['id'] = str(account['_id'])
                    del account['_id']
                formatted_accounts.append(account)

            logger.info(f"Returning {len(formatted_accounts)} accounts")
            return formatted_accounts
        except Exception as e:
            logger.error(f"Failed to get accounts: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"获取社媒账号列表失败: {str(e)}"
            )

    @router.post("/accounts", response_model=dict)
    async def create_social_account(
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """创建社媒账号"""
        logger = logging.getLogger(__name__)
        try:
            account_data = await request.json()
            logger.info(f"Creating account with data: {account_data}")

            account_id = db_service.create_account(account_data)
            account = db_service.get_account(account_id)

            # 转换ObjectId为字符串
            if account and '_id' in account:
                account['id'] = str(account['_id'])
                del account['_id']

            logger.info(f"Account created: {account}")
            return account

        except Exception as e:
            logger.error(f"Failed to create account: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"创建社媒账号失败: {str(e)}"
            )

    from fastapi import Response

    @router.put("/account/{account_id}", response_model=dict)
    async def update_social_account(
        request: Request,
        account_id: str,
        db_service: SocialDatabaseService = Depends(get_social_service),
        response: Response = None
    ):
        """更新单个社媒账号"""
        logger = logging.getLogger(__name__)
        try:
            update_data = await request.json()
            logger.info(f"Updating account {account_id} with data: {update_data}")

            # 先获取当前账号数据
            account = db_service.get_account(account_id)
            if not account:
                raise HTTPException(status_code=404, detail="账号不存在")

            # 执行更新
            result = db_service.update_account(account_id, update_data)
            logger.info(f"Update result: {result}")

            # 获取更新后的数据
            updated_account = db_service.get_account(account_id)
            if not updated_account:
                raise HTTPException(status_code=500, detail="更新后无法获取账号数据")



            # 返回更新后的完整账号数据
            if '_id' in updated_account:
                updated_account['id'] = str(updated_account['_id'])
                del updated_account['_id']
            return updated_account
        except Exception as e:
            logger.error(f"更新账号失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"更新账号失败: {str(e)}"
            )

    # 显式处理OPTIONS预检请求
    @router.options("/account/{account_id}")
    async def options_account(response: Response):
        response.headers.update({
            "Access-Control-Allow-Origin": "http://localhost:5173",
            "Access-Control-Allow-Methods": "PUT, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "86400"
        })
        return {"message": "OK"}

    @router.delete("/account/{account_id}", response_model=dict)
    async def delete_social_account(
        request: Request,
        account_id: str,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """删除单个社媒账号"""
        logger = logging.getLogger(__name__)
        try:
            logger.info(f"Deleting single account: {account_id}")
            result = db_service.delete_account(account_id)
            if not result:
                raise HTTPException(status_code=404, detail="账号不存在")
            return {"deleted": True}
        except Exception as e:
            logger.error(f"Failed to delete account: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"删除社媒账号失败: {str(e)}"
            )

    @router.post("/posts", response_model=dict)
    async def create_social_post(
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """创建社媒内容"""
        try:
            post_data = await request.json()
            post_id = db_service.create_post(post_data)
            return db_service.get_post(post_id)
        except Exception as e:
            raise HTTPException(status_code=500, detail="创建社媒内容失败")

    @router.get("/analytics", response_model=dict)
    async def get_social_analytics(
        app_id: str = None,
        account_id: str = None,
        days: int = 7,
        platform: str = None,
        db_service: SocialDatabaseService = Depends(SocialDatabaseService)
    ):
        """获取社媒分析数据"""
        try:
            # 计算日期范围
            end_date = datetime.datetime.now()
            start_date = end_date - datetime.timedelta(days=days)

            if platform:  # 使用平台适配器获取数据
                from ..core.social.platform_adapter import get_adapter
                adapter = get_adapter(platform)
                return adapter.get_analytics(
                    start_date.strftime('%Y-%m-%d'),
                    end_date.strftime('%Y-%m-%d')
                )
            else:  # 使用数据库服务获取数据
                analytics = db_service.get_analytics(
                    app_id=app_id,
                    account_id=account_id,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d'),
                    platform=platform
                )

                summary = {
                    "total_likes": sum(item.get("likes", 0) for item in analytics),
                    "total_comments": sum(item.get("comments", 0) for item in analytics),
                    "total_shares": sum(item.get("shares", 0) for item in analytics),
                    "total_views": sum(item.get("views", 0) for item in analytics)
                }

                return {
                    "data": analytics,
                    "summary": summary
                }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取分析数据失败: {str(e)}")

    @router.post("/{platform}/auth")
    async def auth_platform(
        platform: str,
        request: Request
    ):
        """平台账号认证"""
        try:
            from ..core.social.platform_adapter import get_adapter
            adapter = get_adapter(platform)
            credentials = await request.json()
            if adapter.authenticate(credentials):
                return {"status": "success", "platform": platform}
            raise HTTPException(status_code=401, detail="Authentication failed")
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

    @router.post("/{platform}/posts")
    async def create_platform_post(
        platform: str,
        request: Request
    ):
        """发布内容到指定平台"""
        try:
            from ..core.social.platform_adapter import get_adapter
            adapter = get_adapter(platform)
            content = await request.json()
            post_id = adapter.post_content(content)
            return {
                "status": "success",
                "platform": platform,
                "post_id": post_id
            }
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

    # WebSocket事件处理
    @sio.on("social_control")
    async def handle_social_control(sid, data, callback=None):
        """控制社媒应用"""
        try:
            action = data.get("action")
            app_id = data.get("app_id")
            device_id = data.get("device_id")

            if action not in ["start", "stop", "clear"]:
                raise ValueError("无效的操作类型")

            # TODO: 实现社媒应用控制逻辑
            result = {"success": True, "action": action}

            if callback:
                await callback(result)

            # 广播状态更新
            await sio.emit(
                "social_status_update",
                {"app_id": app_id, "status": action},
                to=sid
            )
        except Exception as e:
            if callback:
                await callback({"success": False, "error": str(e)})

    # 新增账号分组管理API
    @router.post("/groups", response_model=dict)
    async def create_social_group(
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """创建社媒账号分组"""
        try:
            group_data = await request.json()
            group_id = db_service.create_group(group_data)
            return {"group_id": group_id}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建分组失败: {str(e)}")

    @router.post("/groups/{group_id}/accounts", response_model=dict)
    async def add_account_to_group(
        request: Request,
        group_id: str,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """添加账号到分组"""
        try:
            data = await request.json()
            account_id = data.get("account_id")
            if not account_id:
                raise HTTPException(status_code=400, detail="缺少account_id参数")

            success = db_service.add_account_to_group(group_id, account_id)
            return {"success": success}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"添加账号到分组失败: {str(e)}")

    @router.get("/groups/{group_id}/accounts", response_model=List[dict])
    async def get_group_accounts(
        request: Request,
        group_id: str,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取分组下的账号列表"""
        try:
            accounts = db_service.get_group_accounts(group_id)
            return accounts
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取分组账号失败: {str(e)}")

    @router.post("/accounts/batch_update", response_model=dict)
    async def batch_update_accounts(
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """批量更新账号"""
        try:
            data = await request.json()
            account_ids = data.get("account_ids", [])
            update_data = data.get("update_data", {})

            if not account_ids or not update_data:
                raise HTTPException(status_code=400, detail="缺少必要参数")

            count = db_service.batch_update_accounts(account_ids, update_data)
            return {"updated_count": count}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"批量更新失败: {str(e)}")

    @router.delete("/accounts/batch", response_model=dict)
    async def batch_delete_accounts(
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """批量删除账号"""
        try:
            data = await request.json()
            account_ids = data.get("account_ids", [])

            if not account_ids:
                raise HTTPException(status_code=400, detail="缺少account_ids参数")

            count = db_service.batch_delete_accounts(account_ids)
            return {"deleted_count": count}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")

    @router.get("/workflows", response_model=List[dict])
    async def get_workflows(
        request: Request,
        skip: int = 0,
        limit: int = 100,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取工作流列表"""
        logger = logging.getLogger(__name__)
        try:
            logger.info("Fetching workflows")
            workflows = db_service.get_workflows(skip=skip, limit=limit)
            logger.info(f"Found {len(workflows)} workflows")
            return workflows
        except Exception as e:
            logger.error(f"Failed to get workflows: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"获取工作流列表失败: {str(e)}"
            )

    @router.post("/tasks", response_model=dict)
    async def create_publish_task(
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """创建发布任务"""
        try:
            task_data = await request.json()
            logger = logging.getLogger(__name__)
            logger.info(f"收到创建任务请求: {task_data}")

            # 验证必要字段
            required_fields = ["platform_id", "account_id", "content_path"]
            for field in required_fields:
                if field not in task_data:
                    raise HTTPException(
                        status_code=400,
                        detail=f"缺少必要字段: {field}"
                    )

            # 如果有工作流ID，添加到任务数据中
            if "workflow_id" in task_data:
                workflow_id = task_data["workflow_id"]
                logger.info(f"任务使用工作流: {workflow_id}")

            # 检查是否需要拆分子任务
            selected_files = task_data.get("selected_files", [])
            if selected_files and len(selected_files) > 1:
                logger.info(f"检测到多个文件，将拆分为子任务: {len(selected_files)} 个文件")

                # 生成主任务ID
                main_task_id = str(uuid.uuid4())

                # 创建主任务记录
                main_task_data = {
                    "task_id": main_task_id,
                    "platform_id": task_data["platform_id"],
                    "account_id": task_data["account_id"],
                    "content_path": task_data["content_path"],
                    "schedule_type": task_data.get("schedule_type", "immediate"),
                    "schedule_time": task_data.get("schedule_time"),
                    "workflow_id": task_data.get("workflow_id"),
                    "task_type": "main",
                    "total_subtasks": len(selected_files),
                    "completed_subtasks": 0,
                    "selected_files": selected_files,
                    "status": "pending",
                    "created_at": datetime.datetime.now()
                }

                db_main_task_id = db_service.create_task(main_task_data)
                logger.info(f"创建主任务: {main_task_id}")

                # 为每个文件创建子任务
                subtask_ids = []
                for i, video_file in enumerate(selected_files):
                    subtask_id = str(uuid.uuid4())

                    # 创建子任务数据
                    subtask_data = {
                        "task_id": subtask_id,
                        "parent_task_id": main_task_id,
                        "platform_id": task_data["platform_id"],
                        "account_id": task_data["account_id"],
                        "content_path": task_data["content_path"],
                        "schedule_type": task_data.get("schedule_type", "immediate"),
                        "schedule_time": task_data.get("schedule_time"),
                        "workflow_id": task_data.get("workflow_id"),
                        "task_type": "subtask",
                        "subtask_index": i + 1,
                        "selected_files": [video_file],
                        "video_file": video_file,
                        "status": "pending",
                        "created_at": datetime.datetime.now()
                    }

                    # 创建子任务
                    db_subtask_id = db_service.create_task(subtask_data)
                    subtask_ids.append(subtask_id)
                    logger.info(f"创建子任务 {i+1}/{len(selected_files)}: {subtask_id}, 文件: {video_file}")

                # 返回主任务信息
                return {
                    "task_id": main_task_id,
                    "status": "pending",
                    "task_type": "main",
                    "subtask_ids": subtask_ids,
                    "total_subtasks": len(selected_files)
                }

            else:
                # 单个文件或没有选择文件，创建普通任务
                task_id = str(uuid.uuid4())

                # 创建任务记录
                task_record = {
                    "task_id": task_id,
                    "platform_id": task_data["platform_id"],
                    "account_id": task_data["account_id"],
                    "content_path": task_data["content_path"],
                    "schedule_type": task_data.get("schedule_type", "immediate"),
                    "schedule_time": task_data.get("schedule_time"),
                    "workflow_id": task_data.get("workflow_id"),
                    "task_type": "single",
                    "status": "pending",
                    "created_at": datetime.datetime.now()
                }

                # 如果有选中文件，添加到任务记录中
                if selected_files:
                    task_record["selected_files"] = selected_files
                    task_record["video_file"] = selected_files[0] if selected_files else None

                db_task_id = db_service.create_task(task_record)
                logger.info(f"创建单个任务: {task_id}")

                return {
                    "task_id": task_id,
                    "status": "pending",
                    "task_type": "single"
                }

        except HTTPException:
            raise
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"创建任务失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"创建任务失败: {str(e)}"
            )

    # 获取Core服务客户端
    async def get_task_client(request: Request):
        """获取任务服务客户端"""
        try:
            # 从配置获取Consul地址
            from app.config.database import DatabaseConfig
            db_config = DatabaseConfig()

            # 解析Consul URL
            from urllib.parse import urlparse
            consul_url = urlparse(db_config.consul_url)
            consul_host = consul_url.hostname or "localhost"
            consul_port = consul_url.port or 8500

            # 从Consul获取Core服务
            consul_discovery = ConsulDiscovery(
                consul_host=consul_host,
                consul_port=consul_port
            )

            # 获取Core服务实例
            services = consul_discovery.get_all_services("thunderhub-core")

            if not services:
                # 如果没有找到服务，使用默认地址
                logger = logging.getLogger(__name__)
                logger.warning("无法从Consul获取Core服务列表，使用默认地址")
                return TaskServiceClient()

            # 使用第一个可用的服务
            # services是一个字典，不是列表，所以我们需要获取字典的值
            service_values = list(services.values())
            if service_values:
                service = service_values[0]
            else:
                # 如果列表为空但没有触发上面的条件（可能是空字典但不是None）
                logger = logging.getLogger(__name__)
                logger.warning("Core服务列表为空，使用默认地址")
                return TaskServiceClient()
            return TaskServiceClient(
                host=service.get("host", "localhost"),
                port=service.get("port", 50051)
            )
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"获取任务服务客户端失败: {str(e)}", exc_info=True)
            # 使用默认地址
            return TaskServiceClient()

    @router.put("/tasks/{task_id}/config", response_model=dict)
    async def update_task_config(
        task_id: str,
        config: dict,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """更新任务配置"""
        logger = logging.getLogger(__name__)
        try:
            # 获取任务信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            logger.info(f"=== 更新任务{task_id}配置 ===")
            logger.info(f"完整配置数据: {config}")
            logger.info(f"配置数据类型: {type(config)}")
            logger.info(f"配置数据键: {list(config.keys()) if isinstance(config, dict) else 'Not a dict'}")

            # 特别检查音乐配置
            if 'selectedMusic' in config:
                selected_music = config['selectedMusic']
                logger.info(f"✅ 检测到selectedMusic字段: {selected_music}")
                logger.info(f"selectedMusic类型: {type(selected_music)}")
                logger.info(f"selectedMusic长度: {len(selected_music) if isinstance(selected_music, list) else 'Not a list'}")
            else:
                logger.warning("❌ 未检测到selectedMusic字段")

            # 更新任务配置
            update_data = {
                "metadata": config,
                "updated_at": datetime.datetime.now()
            }

            success = db_service.update_task(task_id, update_data)
            if not success:
                raise HTTPException(status_code=500, detail="更新任务配置失败")

            return {
                "success": True,
                "message": "任务配置更新成功"
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"更新任务配置异常: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"更新任务配置失败: {str(e)}")

    @router.post("/tasks/{task_id}/start", response_model=dict)
    async def start_task(
        task_id: str,
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """开始执行任务"""
        logger = logging.getLogger(__name__)
        try:
            # 获取任务信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 检查任务状态
            if task.get("status") not in ["pending", "paused", "failed", "partial_completed"]:
                raise HTTPException(status_code=400, detail=f"任务状态为{task.get('status')}，无法开始执行")

            # 记录任务重启信息
            if task.get("status") in ["failed", "partial_completed"]:
                logger.info(f"重新启动失败的任务: {task_id}, 原状态: {task.get('status')}")

            # 获取设备ID
            device_id = task.get("device_id")
            if not device_id:
                # 根据账号和平台选择合适的设备
                account_id = task.get("account_id")
                platform_id = task.get("platform_id")

                logger.info(f"为任务{task_id}分配设备，账号ID: {account_id}, 平台ID: {platform_id}")

                if account_id and platform_id:
                    # 查询账号与设备的映射关系
                    mapping = db_service.db.device_account_mappings.find_one({
                        "account_id": account_id,
                        "platform_id": platform_id,
                        "status": "active"
                    })

                    if mapping and "device_id" in mapping:
                        device_id = str(mapping["device_id"])
                        logger.info(f"根据账号{account_id}和平台{platform_id}找到设备映射: {device_id}")

                        # 更新任务的设备ID
                        db_service.update_task(task_id, {"device_id": device_id})
                        logger.info(f"为任务{task_id}分配设备: {device_id}")
                    else:
                        # 如果没有找到特定平台的映射，尝试只根据账号查找
                        logger.info(f"未找到账号{account_id}和平台{platform_id}的映射，尝试只根据账号查找")
                        mapping = db_service.db.device_account_mappings.find_one({
                            "account_id": account_id,
                            "status": "active"
                        })

                        if mapping and "device_id" in mapping:
                            device_id = str(mapping["device_id"])
                            logger.info(f"根据账号{account_id}找到设备映射: {device_id}")

                            # 更新任务的设备ID
                            db_service.update_task(task_id, {"device_id": device_id})
                            logger.info(f"为任务{task_id}分配设备: {device_id}")
                        else:
                            logger.warning(f"未找到账号{account_id}的设备映射，将使用默认设备分配逻辑")

                # 如果仍然没有找到设备，使用默认分配逻辑
                if not device_id:
                    logger.info("使用默认设备分配逻辑")
                    from app.core.schemas.device_repository import DeviceRepository
                    device_repo = DeviceRepository(request.app.state.mongo_db)

                    # 尝试获取可用设备
                    devices = await device_repo.get_devices(
                        query={"status": "running"},
                        limit=1
                    )

                    # 如果没有运行中的设备，获取任何状态的设备
                    if not devices:
                        devices = await device_repo.get_devices(limit=1)

                    if not devices:
                        raise HTTPException(status_code=400, detail="没有可用的设备")

                    # 设备是字典，使用字典访问方式获取ID
                    device_id = str(devices[0].get("_id", ""))

                    # 更新任务的设备ID
                    db_service.update_task(task_id, {"device_id": device_id})
                    logger.info(f"为任务{task_id}分配默认设备: {device_id}")

            # 获取任务服务客户端
            task_client = await get_task_client(request)

            # 更新任务数据
            task_data = {
                "task_id": task_id,
                "platform_id": task.get("platform_id"),
                "account_id": task.get("account_id"),
                "device_id": device_id,
                "content_path": task.get("content_path")
            }

            # 添加任务元数据（包含contentType等配置信息）
            if "metadata" in task and task["metadata"]:
                task_data["metadata"] = task["metadata"]
                logger.info(f"添加任务元数据到Core服务: {task['metadata']}")

            # 检查是否是YouTube上传任务
            is_youtube_task = False

            # 方法1：检查平台ID是否包含"youtube"
            if task.get("platform_id") and "youtube" in str(task.get("platform_id")).lower():
                is_youtube_task = True
                logger.info(f"通过平台ID检测到YouTube任务: {task.get('platform_id')}")

            # 方法2：检查任务数据中是否有YouTube特有的字段
            if "metadata" in task and isinstance(task["metadata"], dict):
                metadata = task["metadata"]
                if "titleTemplate" in metadata or "privacyStatus" in metadata:
                    is_youtube_task = True
                    logger.info("通过元数据字段检测到YouTube任务")

            # 方法3：检查任务是否有folder_path而不是content_path
            if "folder_path" in task and not task.get("content_path"):
                is_youtube_task = True
                # 将folder_path设置为content_path
                task_data["content_path"] = task.get("folder_path")
                logger.info(f"通过folder_path字段检测到YouTube任务: {task.get('folder_path')}")

            # 方法4：检查内容路径是否包含"youtube"
            if task.get("content_path") and "youtube" in str(task.get("content_path")).lower():
                is_youtube_task = True
                logger.info(f"通过内容路径检测到YouTube任务: {task.get('content_path')}")

            # 方法5：检查任务ID是否包含"youtube"
            if task_id and "youtube" in str(task_id).lower():
                is_youtube_task = True
                logger.info(f"通过任务ID检测到YouTube任务: {task_id}")

            # 如果是YouTube任务，更新平台ID
            if is_youtube_task:
                original_platform_id = task.get('platform_id')
                logger.info(f"检测到YouTube任务，原始平台ID: {original_platform_id}")

                # 记录数据库中的所有平台，用于调试
                try:
                    platforms = list(db_service.db.social_platforms.find({}))
                    logger.info(f"数据库中的平台记录: {[p.get('id') for p in platforms]}")

                    # 查找YouTube相关平台
                    youtube_platforms = []
                    for platform in platforms:
                        platform_id = platform.get("id", "")
                        platform_name = platform.get("name", "")
                        if "youtube" in str(platform_id).lower() or "youtube" in str(platform_name).lower():
                            youtube_platforms.append(platform)

                    if youtube_platforms:
                        # 使用找到的第一个YouTube平台
                        youtube_platform = youtube_platforms[0]

                        # 尝试获取平台ID，优先使用platform字段（这是平台的id字段）
                        if "platform" in youtube_platform:
                            youtube_platform_id = youtube_platform.get("platform")
                            logger.info(f"使用platform字段作为平台ID: {youtube_platform_id}")
                        elif "id" in youtube_platform:
                            youtube_platform_id = youtube_platform.get("id")
                            logger.info(f"使用id字段作为平台ID: {youtube_platform_id}")
                        else:
                            # 如果都没有，使用name字段的小写形式
                            youtube_platform_id = youtube_platform.get("name", "youtube").lower()
                            logger.info(f"使用name字段的小写形式作为平台ID: {youtube_platform_id}")

                        logger.info(f"找到YouTube相关平台: {[(p.get('platform', p.get('id')), p.get('name')) for p in youtube_platforms]}")
                        logger.info(f"使用平台ID: {youtube_platform_id}")

                        # 更新任务数据中的平台ID
                        task_data["platform_id"] = youtube_platform_id

                        # 记录更新后的任务数据
                        logger.info(f"更新后的任务数据: {task_data}")
                        logger.info(f"已将平台ID从 {original_platform_id} 更新为 {youtube_platform_id}")
                    else:
                        logger.warning("在数据库中未找到YouTube相关平台记录，保留原始平台ID")
                except Exception as db_error:
                    logger.error(f"查询平台记录失败: {str(db_error)}")

            logger.info(f"任务数据: {task_data}")

            # 添加可选字段
            if "workflow_id" in task and task["workflow_id"]:
                try:
                    task_data["workflow_id"] = str(task["workflow_id"])
                except Exception as e:
                    logger.warning(f"转换workflow_id失败: {str(e)}")

            # 创建Core任务
            create_result = await task_client.create_task(task_data)
            if not create_result.get("success"):
                raise HTTPException(
                    status_code=500,
                    detail=f"创建Core任务失败: {create_result.get('error')}"
                )

            # 开始执行任务
            start_result = await task_client.start_task(task_id)
            if not start_result.get("success"):
                raise HTTPException(
                    status_code=500,
                    detail=f"开始执行任务失败: {start_result.get('error')}"
                )

            # 更新任务状态
            update_data = {
                "status": "running",
                "start_time": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now()
            }
            db_service.update_task(task_id, update_data)

            # 发布任务状态更新到Redis
            try:
                if hasattr(request.app.state, 'redis'):
                    redis_client = request.app.state.redis
                    # 构建状态数据
                    status_data = {
                        "task_id": task_id,
                        "status": "running",
                        "progress": 0,
                        "start_time": update_data["start_time"],
                        "logs": [
                            {
                                "message": "任务开始执行",
                                "level": "info",
                                "timestamp": update_data["start_time"]
                            }
                        ]
                    }
                    # 发布到Redis
                    channel = f"task:{task_id}:status"
                    await redis_client.publish(channel, json.dumps(status_data))
                    logger.info(f"已发布任务{task_id}的状态更新到Redis")
            except Exception as e:
                logger.error(f"发布任务状态到Redis失败: {str(e)}", exc_info=True)

            # 构建更详细的响应
            response_data = {
                "task_id": task_id,
                "status": "running",
                "message": "任务已开始执行",
                "details": {
                    "platform_id": task.get("platform_id"),
                    "account_id": task.get("account_id"),
                    "device_id": device_id,
                    "content_path": task.get("content_path"),
                    "start_time": update_data["start_time"]
                },
                "steps": [
                    {"step": 1, "name": "准备任务", "status": "completed", "message": "任务准备就绪"},
                    {"step": 2, "name": "连接设备", "status": "in_progress", "message": "正在连接设备..."},
                    {"step": 3, "name": "启动应用", "status": "pending", "message": "等待设备连接完成"},
                    {"step": 4, "name": "执行操作", "status": "pending", "message": "等待应用启动完成"},
                    {"step": 5, "name": "完成任务", "status": "pending", "message": "等待操作执行完成"}
                ],
                "current_step": 2,
                "progress": 0
            }

            return response_data
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"开始执行任务异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"开始执行任务失败: {str(e)}"
            )
        finally:
            # 关闭客户端连接
            if 'task_client' in locals() and task_client is not None:
                try:
                    await task_client.close()
                except Exception as e:
                    logger.error(f"关闭任务客户端连接异常: {str(e)}")

    @router.post("/tasks/{task_id}/pause", response_model=dict)
    async def pause_task(
        task_id: str,
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """暂停任务"""
        logger = logging.getLogger(__name__)
        try:
            # 获取任务信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 检查任务状态
            if task.get("status") != "running":
                raise HTTPException(status_code=400, detail=f"任务状态为{task.get('status')}，无法暂停")

            # 获取任务服务客户端
            task_client = await get_task_client(request)

            # 暂停任务
            result = await task_client.pause_task(task_id)
            if not result.get("success"):
                raise HTTPException(
                    status_code=500,
                    detail=f"暂停任务失败: {result.get('error')}"
                )

            # 更新任务状态
            db_service.update_task(task_id, {"status": "paused"})

            return {
                "task_id": task_id,
                "status": "paused",
                "message": "任务已暂停"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"暂停任务异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"暂停任务失败: {str(e)}"
            )
        finally:
            # 关闭客户端连接
            if 'task_client' in locals() and task_client is not None:
                try:
                    await task_client.close()
                except Exception as e:
                    logger.error(f"关闭任务客户端连接异常: {str(e)}")

    @router.post("/tasks/{task_id}/cancel", response_model=dict)
    async def cancel_task(
        task_id: str,
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """取消任务"""
        logger = logging.getLogger(__name__)
        try:
            # 获取任务信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 检查任务状态
            if task.get("status") not in ["pending", "running", "paused"]:
                raise HTTPException(status_code=400, detail=f"任务状态为{task.get('status')}，无法取消")

            # 获取任务服务客户端
            task_client = await get_task_client(request)

            # 取消任务
            result = await task_client.cancel_task(task_id)
            if not result.get("success"):
                raise HTTPException(
                    status_code=500,
                    detail=f"取消任务失败: {result.get('error')}"
                )

            # 更新任务状态
            db_service.update_task(task_id, {"status": "canceled"})

            return {
                "task_id": task_id,
                "status": "canceled",
                "message": "任务已取消"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"取消任务异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"取消任务失败: {str(e)}"
            )
        finally:
            # 关闭客户端连接
            if 'task_client' in locals() and task_client is not None:
                try:
                    await task_client.close()
                except Exception as e:
                    logger.error(f"关闭任务客户端连接异常: {str(e)}")

    @router.get("/tasks/{task_id}/status", response_model=dict)
    async def get_task_status(
        task_id: str,
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取任务状态"""
        logger = logging.getLogger(__name__)
        try:
            # 获取任务信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 如果任务状态是pending，直接返回数据库中的状态，但添加更多详细信息
            if task.get("status") == "pending":
                return {
                    "task_id": task_id,
                    "status": "pending",
                    "progress": 0,
                    "start_time": "",
                    "estimated_end_time": "",
                    "device_usage": {
                        "cpu": 0,
                        "memory": 0,
                        "network": "未连接"
                    },
                    "logs": [],
                    "details": {
                        "platform_id": task.get("platform_id"),
                        "account_id": task.get("account_id"),
                        "device_id": task.get("device_id"),
                        "content_path": task.get("content_path"),
                        "created_at": task.get("created_at", "").isoformat() if hasattr(task.get("created_at", ""), "isoformat") else task.get("created_at", "")
                    },
                    "steps": [
                        {"step": 1, "name": "准备任务", "status": "completed", "message": "任务准备就绪"},
                        {"step": 2, "name": "连接设备", "status": "pending", "message": "等待任务开始"},
                        {"step": 3, "name": "启动应用", "status": "pending", "message": "等待任务开始"},
                        {"step": 4, "name": "执行操作", "status": "pending", "message": "等待任务开始"},
                        {"step": 5, "name": "完成任务", "status": "pending", "message": "等待任务开始"}
                    ],
                    "current_step": 1,
                    "message": "任务已创建，等待执行"
                }

            # 获取任务服务客户端
            task_client = await get_task_client(request)

            # 获取任务状态
            status = await task_client.get_task_status(task_id)

            # 更新任务状态到数据库
            if status.get("status") != task.get("status"):
                db_service.update_task(task_id, {"status": status.get("status")})

            return status
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务状态异常: {str(e)}", exc_info=True)

            # 如果获取状态失败，返回数据库中的基本状态
            if task:
                return {
                    "task_id": task_id,
                    "status": task.get("status", "unknown"),
                    "progress": 0,
                    "start_time": "",
                    "estimated_end_time": "",
                    "device_usage": {
                        "cpu": 0,
                        "memory": 0,
                        "network": "未知"
                    },
                    "logs": []
                }

            raise HTTPException(
                status_code=500,
                detail=f"获取任务状态失败: {str(e)}"
            )
        finally:
            # 关闭客户端连接
            if 'task_client' in locals() and task_client is not None:
                try:
                    await task_client.close()
                except Exception as e:
                    logger.error(f"关闭任务客户端连接异常: {str(e)}")

    @router.get("/tasks/{task_id}/logs", response_model=List[dict])
    async def get_task_logs(
        task_id: str,
        request: Request,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取任务日志"""
        logger = logging.getLogger(__name__)
        try:
            # 获取任务信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 获取任务服务客户端
            task_client = await get_task_client(request)

            # 获取任务日志
            logs = await task_client.get_task_logs(task_id)

            return logs
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务日志异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"获取任务日志失败: {str(e)}"
            )
        finally:
            # 关闭客户端连接
            if 'task_client' in locals() and task_client is not None:
                try:
                    await task_client.close()
                except Exception as e:
                    logger.error(f"关闭任务客户端连接异常: {str(e)}")

    @router.get("/tasks/{task_id}/result", response_model=dict)
    async def get_task_result(
        task_id: str,
        db_service: SocialDatabaseService = Depends(get_social_service)
    ):
        """获取任务执行结果统计"""
        logger = logging.getLogger(__name__)
        try:
            logger.info(f"获取任务结果: {task_id}")

            # 获取任务基本信息
            task = db_service.get_task(task_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")

            # 获取任务日志，统计结果
            logs = list(db_service.db.social_task_logs.find(
                {"task_id": task_id}
            ).sort("created_at", 1))

            # 统计各种状态的数量
            success_count = 0
            warning_count = 0
            error_count = 0
            total_steps = 0

            # 分析日志，统计执行结果
            for log in logs:
                message = log.get("message", "").lower()
                level = log.get("level", "info").lower()

                # 统计步骤执行结果
                if "步骤执行结果" in log.get("message", ""):
                    total_steps += 1
                    if "成功" in message or level == "success":
                        success_count += 1
                    elif "警告" in message or level == "warning":
                        warning_count += 1
                    elif "失败" in message or level == "error":
                        error_count += 1
                elif level == "error":
                    error_count += 1
                elif level == "warning":
                    warning_count += 1
                elif "成功" in message and level == "info":
                    success_count += 1

            # 如果没有详细的步骤统计，根据任务状态给出基本统计
            if total_steps == 0:
                task_status = task.get("status", "unknown")
                if task_status == "completed":
                    success_count = 1
                    total_steps = 1
                elif task_status == "failed":
                    error_count = 1
                    total_steps = 1
                elif task_status == "running":
                    # 任务还在运行中，暂时没有结果
                    total_steps = 0
                else:
                    total_steps = 1

            # 获取任务详细信息
            task_details = {
                "task_id": task_id,
                "status": task.get("status", "unknown"),
                "platform_id": task.get("platform_id", ""),
                "account_id": task.get("account_id", ""),
                "content_path": task.get("content_path", ""),
                "created_at": task.get("created_at", ""),
                "updated_at": task.get("updated_at", ""),
                "start_time": task.get("start_time", ""),
                "end_time": task.get("end_time", "")
            }

            # 构建结果统计
            result_summary = {
                "success": success_count,
                "warning": warning_count,
                "error": error_count,
                "total": max(total_steps, success_count + warning_count + error_count)
            }

            # 获取最近的关键日志
            key_logs = []
            for log in logs[-20:]:  # 最近20条日志
                level = log.get("level", "info")
                if level in ["error", "warning"] or "成功" in log.get("message", ""):
                    key_logs.append({
                        "message": log.get("message", ""),
                        "level": level,
                        "timestamp": log.get("timestamp", ""),
                        "created_at": log.get("created_at", "")
                    })

            return {
                "task_id": task_id,
                "summary": result_summary,
                "details": task_details,
                "key_logs": key_logs,
                "total_logs": len(logs)
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务结果失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"获取任务结果失败: {str(e)}"
            )

    return router
