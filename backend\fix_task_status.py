#!/usr/bin/env python3
"""
修复特定任务的状态
直接将失败的任务状态更新到MongoDB
"""

import asyncio
import json
import logging
from datetime import datetime
from pymongo import MongoClient
import redis.asyncio as redis

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def fix_task_status():
    """修复特定任务的状态"""
    try:
        # 直接使用配置
        redis_url = "redis://localhost:6379"
        mongo_url = "mongodb://localhost:27017"
        mongo_db_name = "social_media_automation"
        
        # 要修复的任务ID
        task_id = "ded8ab69-4f04-440f-856f-d645f31b64d7"
        
        logger.info(f"开始修复任务: {task_id}")
        
        # 连接MongoDB
        mongo_client = MongoClient(mongo_url)
        mongo_db = mongo_client[mongo_db_name]
        logger.info("MongoDB连接成功")
        
        # 检查任务当前状态
        current_task = mongo_db.social_tasks.find_one({"task_id": task_id})
        if not current_task:
            logger.error(f"未找到任务: {task_id}")
            return
            
        logger.info(f"当前任务状态:")
        logger.info(f"  状态: {current_task.get('status')}")
        logger.info(f"  进度: {current_task.get('progress', 0)}%")
        logger.info(f"  更新时间: {current_task.get('updated_at')}")
        logger.info(f"  开始时间: {current_task.get('start_time')}")
        logger.info(f"  结束时间: {current_task.get('end_time')}")
        
        # 由于用户反馈任务已失败，直接设置为失败状态
        logger.info("根据用户反馈，任务已失败，直接更新状态...")

        # 直接设置为失败状态
        update_data = {
            "status": "failed",
            "progress": 0,
            "updated_at": datetime.now(),
            "end_time": datetime.now().isoformat()
        }

        result = mongo_db.social_tasks.update_one(
            {"task_id": task_id},
            {"$set": update_data}
        )

        if result.modified_count > 0:
            logger.info("✅ 成功设置任务状态为失败")
        else:
            logger.error("❌ 设置任务状态失败")
        
        # 检查更新后的状态
        updated_task = mongo_db.social_tasks.find_one({"task_id": task_id})
        if updated_task:
            logger.info(f"更新后的任务状态:")
            logger.info(f"  状态: {updated_task.get('status')}")
            logger.info(f"  进度: {updated_task.get('progress', 0)}%")
            logger.info(f"  更新时间: {updated_task.get('updated_at')}")
            logger.info(f"  结束时间: {updated_task.get('end_time')}")
        
        # 同时检查子任务状态
        subtasks = list(mongo_db.social_tasks.find({"parent_task_id": task_id}))
        if subtasks:
            logger.info(f"找到 {len(subtasks)} 个子任务，检查状态:")
            for subtask in subtasks:
                logger.info(f"  子任务 {subtask.get('task_id')}: {subtask.get('status')}")
                
                # 如果子任务还在运行，也设置为失败
                if subtask.get('status') in ['running', 'pending']:
                    logger.info(f"  设置子任务 {subtask.get('task_id')} 为失败")
                    mongo_db.social_tasks.update_one(
                        {"task_id": subtask.get('task_id')},
                        {"$set": {
                            "status": "failed",
                            "updated_at": datetime.now(),
                            "end_time": datetime.now().isoformat()
                        }}
                    )
        
        mongo_client.close()
        logger.info("修复完成")
        
    except Exception as e:
        logger.error(f"修复失败: {str(e)}", exc_info=True)


async def check_backend_sync_service():
    """检查Backend同步服务是否正常运行"""
    try:
        redis_url = "redis://localhost:6379"
        
        # 连接Redis
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()
        logger.info("Redis连接成功")
        
        # 检查是否有订阅者监听任务状态频道
        channels_info = await redis_client.pubsub_channels("task:*:status")
        logger.info(f"任务状态频道数量: {len(channels_info)}")
        
        # 检查Redis中的任务状态键
        task_keys = await redis_client.keys("task:*:latest")
        logger.info(f"Redis中有 {len(task_keys)} 个任务状态键")
        
        if task_keys:
            logger.info("最近的5个任务状态:")
            for i, key in enumerate(task_keys[:5]):
                try:
                    status_json = await redis_client.get(key)
                    if status_json:
                        status_data = json.loads(status_json)
                        logger.info(f"  {key.decode()}: {status_data.get('status')} ({status_data.get('progress', 0)}%)")
                except Exception as e:
                    logger.error(f"  读取 {key.decode()} 失败: {str(e)}")
        
        # 发布一个测试消息看是否有订阅者
        test_channel = "task:test:status"
        test_data = {
            "task_id": "test",
            "status": "test",
            "timestamp": datetime.now().isoformat()
        }
        
        subscribers = await redis_client.publish(test_channel, json.dumps(test_data))
        logger.info(f"测试消息发布结果: {subscribers} 个订阅者接收")
        
        if subscribers == 0:
            logger.warning("❌ 没有订阅者监听任务状态频道！Backend同步服务可能没有运行")
        else:
            logger.info("✅ 有订阅者监听任务状态频道，Backend同步服务正常")
        
        await redis_client.close()
        
    except Exception as e:
        logger.error(f"检查Backend同步服务失败: {str(e)}", exc_info=True)


async def main():
    """主函数"""
    logger.info("开始修复任务状态")

    # 修复特定任务状态
    logger.info("=== 修复特定任务状态 ===")
    await fix_task_status()


if __name__ == "__main__":
    asyncio.run(main())
