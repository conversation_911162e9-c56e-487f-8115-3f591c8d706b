"""
Redis同步服务
负责从Redis获取设备状态并通过WebSocket推送给前端
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional
import redis.asyncio as redis
from datetime import datetime

from app.services.device_history_service import DeviceHistoryService

logger = logging.getLogger(__name__)

class RedisSyncService:
    """Redis同步服务类"""

    def __init__(self,
                 redis_url: str,
                 socketio,
                 mongo_db,
                 snapshot_interval: int = 300):
        """初始化Redis同步服务

        Args:
            redis_url: Redis服务URL
            socketio: Socket.IO实例，用于推送状态给前端
            mongo_db: MongoDB数据库实例，用于保存状态快照
            snapshot_interval: 快照间隔（秒），默认300秒（5分钟）
        """
        self.redis_url = redis_url
        self.socketio = socketio
        self.mongo_db = mongo_db
        self.snapshot_interval = snapshot_interval
        self.redis_client = None
        self.pubsub = None
        self.is_running = False
        self.sync_task = None
        self.snapshot_task = None

        # 初始化设备历史服务
        self.history_service = DeviceHistoryService(mongo_db)

        logger.info(f"Redis同步服务初始化，快照间隔: {snapshot_interval}秒")

    async def start(self) -> None:
        """启动同步服务"""
        if self.is_running:
            logger.warning("Redis同步服务已在运行中")
            return

        try:
            logger.info(f"正在连接Redis: {self.redis_url}")

            # 初始化Redis连接
            self.redis_client = redis.Redis.from_url(self.redis_url)

            # 测试连接
            try:
                ping_result = await self.redis_client.ping()
                logger.info(f"Redis连接成功，ping结果: {ping_result}")
            except Exception as e:
                logger.error(f"Redis ping测试失败: {str(e)}")
                raise

            # 检查Redis中的设备数据
            try:
                # 获取与设备相关的键
                device_keys = await self.redis_client.keys("device:*")
                if device_keys:
                    logger.info(f"Redis中找到{len(device_keys)}个设备相关的键")
                    # 显示前5个键
                    if len(device_keys) > 0:
                        logger.info(f"设备键示例: {device_keys[:5]}")
                else:
                    logger.warning("Redis中没有找到任何设备相关的键")

                # 检查设备集合
                device_set = await self.redis_client.exists("devices:all")
                if device_set:
                    device_count = await self.redis_client.scard("devices:all")
                    logger.info(f"Redis中的'devices:all'集合包含{device_count}个设备")
                else:
                    logger.warning("Redis中没有找到'devices:all'集合")

                # 获取所有键
                all_keys = await self.redis_client.keys("*")
                logger.info(f"Redis中共有{len(all_keys)}个键")
                # 显示前20个键
                if len(all_keys) > 0:
                    logger.info(f"Redis键示例: {all_keys[:20]}")
            except Exception as e:
                logger.error(f"检查Redis设备数据失败: {str(e)}")
                # 继续执行，不抛出异常

            # 初始化发布/订阅
            self.pubsub = self.redis_client.pubsub()

            # 订阅设备变更频道
            try:
                await self.pubsub.subscribe("device:all:changes")
                logger.info("已订阅设备变更频道 'device:all:changes'")

                # 订阅任务状态频道
                await self.pubsub.psubscribe("task:*:status")
                logger.info("已订阅任务状态频道 'task:*:status'")

                # 检查订阅状态
                try:
                    # 尝试获取已订阅的频道
                    # 不同版本的Redis客户端可能有不同的API
                    if hasattr(self.pubsub, 'channels') and callable(getattr(self.pubsub, 'channels', None)):
                        # 如果channels是方法
                        channels = await self.pubsub.channels()
                        logger.info(f"当前订阅的频道: {channels}")
                    elif hasattr(self.pubsub, 'channels') and isinstance(self.pubsub.channels, dict):
                        # 如果channels是字典属性
                        channels = list(self.pubsub.channels.keys())
                        logger.info(f"当前订阅的频道: {channels}")
                    elif hasattr(self.pubsub, 'patterns'):
                        # 尝试获取patterns
                        patterns = self.pubsub.patterns
                        logger.info(f"当前订阅的模式: {patterns}")
                    else:
                        logger.warning("无法获取已订阅的频道信息")

                    # 同时显示patterns信息（如果存在）
                    if hasattr(self.pubsub, 'patterns'):
                        patterns = self.pubsub.patterns
                        logger.info(f"当前订阅的模式: {patterns}")

                except Exception as e:
                    logger.error(f"获取订阅频道信息异常: {str(e)}")
            except Exception as e:
                logger.error(f"订阅设备变更频道失败: {str(e)}")
                raise

            # 启动同步任务
            self.is_running = True
            self.sync_task = asyncio.create_task(self._sync_loop())
            self.snapshot_task = asyncio.create_task(self._snapshot_loop())

            logger.info("Redis同步服务已启动")

        except Exception as e:
            logger.error(f"启动Redis同步服务异常: {str(e)}", exc_info=True)
            if self.redis_client:
                try:
                    await self.redis_client.close()
                except Exception as close_error:
                    logger.error(f"关闭Redis连接异常: {str(close_error)}")
                self.redis_client = None

    async def stop(self) -> None:
        """停止同步服务"""
        if not self.is_running:
            return

        self.is_running = False

        # 取消同步任务
        if self.sync_task:
            self.sync_task.cancel()
            try:
                await self.sync_task
            except asyncio.CancelledError:
                pass
            self.sync_task = None

        # 取消快照任务
        if self.snapshot_task:
            self.snapshot_task.cancel()
            try:
                await self.snapshot_task
            except asyncio.CancelledError:
                pass
            self.snapshot_task = None

        # 关闭发布/订阅
        if self.pubsub:
            await self.pubsub.unsubscribe()
            await self.pubsub.close()
            self.pubsub = None

        # 关闭Redis连接
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None

        logger.info("Redis同步服务已停止")

    async def _sync_loop(self) -> None:
        """同步循环，监听Redis发布/订阅消息"""
        try:
            logger.info("开始监听设备状态变更")

            # 检查订阅状态
            channels = []
            try:
                # 尝试获取已订阅的频道
                # 不同版本的Redis客户端可能有不同的API
                if hasattr(self.pubsub, 'channels') and callable(getattr(self.pubsub, 'channels', None)):
                    # 如果channels是方法
                    channels = await self.pubsub.channels()
                elif hasattr(self.pubsub, 'channels') and isinstance(self.pubsub.channels, dict):
                    # 如果channels是字典属性
                    channels = list(self.pubsub.channels.keys())
                elif hasattr(self.pubsub, 'patterns'):
                    # 尝试获取patterns
                    patterns = self.pubsub.patterns
                    logger.info(f"当前订阅的模式: {patterns}")
                else:
                    logger.warning("无法获取已订阅的频道信息")
            except Exception as e:
                logger.error(f"获取订阅频道信息异常: {str(e)}")

            logger.info(f"当前订阅的频道: {channels}")

            # 如果没有订阅任何频道，重新订阅
            if not channels:
                logger.warning("没有订阅任何频道，尝试重新订阅")
                try:
                    await self.pubsub.subscribe("device:all:changes")
                    logger.info("已重新订阅设备变更频道")
                except Exception as e:
                    logger.error(f"重新订阅频道失败: {str(e)}")

            # 记录循环开始时间
            start_time = time.time()
            message_count = 0
            last_log_time = start_time

            while self.is_running:
                try:
                    # 获取消息
                    message = await self.pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)

                    if message:
                        # 处理消息
                        await self._handle_message(message)
                        message_count += 1

                    # 每60秒记录一次状态
                    current_time = time.time()
                    if current_time - last_log_time > 60:
                        elapsed = current_time - start_time
                        logger.info(f"同步循环运行中: 已处理{message_count}条消息，运行时间: {elapsed:.1f}秒")
                        last_log_time = current_time

                        # 检查订阅状态
                        try:
                            channels = []
                            # 尝试获取已订阅的频道
                            # 不同版本的Redis客户端可能有不同的API
                            if hasattr(self.pubsub, 'channels') and callable(getattr(self.pubsub, 'channels', None)):
                                # 如果channels是方法
                                channels = await self.pubsub.channels()
                            elif hasattr(self.pubsub, 'channels') and isinstance(self.pubsub.channels, dict):
                                # 如果channels是字典属性
                                channels = list(self.pubsub.channels.keys())
                            elif hasattr(self.pubsub, 'patterns'):
                                # 尝试获取patterns
                                patterns = self.pubsub.patterns
                                logger.info(f"当前订阅的模式: {patterns}")
                            else:
                                logger.warning("无法获取已订阅的频道信息")

                            logger.info(f"当前订阅的频道: {channels}")

                            # 同时显示patterns信息
                            if hasattr(self.pubsub, 'patterns'):
                                patterns = self.pubsub.patterns
                                logger.info(f"当前订阅的模式: {patterns}")

                            # 如果没有订阅任何频道，重新订阅
                            if not channels:
                                logger.warning("没有订阅任何频道，尝试重新订阅")
                                await self.pubsub.subscribe("device:all:changes")
                                logger.info("已重新订阅设备变更频道")
                        except Exception as e:
                            logger.error(f"检查订阅状态失败: {str(e)}")

                    # 短暂休眠，避免CPU占用过高
                    await asyncio.sleep(0.01)

                except redis.RedisError as e:
                    logger.error(f"Redis操作异常: {str(e)}")
                    # 短暂休眠后继续
                    await asyncio.sleep(1.0)
                except Exception as e:
                    logger.error(f"处理消息异常: {str(e)}", exc_info=True)
                    # 短暂休眠后继续
                    await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            logger.info("同步循环被取消")
            raise
        except Exception as e:
            logger.error(f"同步循环异常: {str(e)}", exc_info=True)
            # 出现异常后尝试重启同步任务
            if self.is_running:
                logger.info("尝试重启同步任务")
                self.sync_task = asyncio.create_task(self._sync_loop())

    async def _snapshot_loop(self) -> None:
        """快照循环，定期将Redis数据保存到MongoDB"""
        try:
            logger.info(f"开始设备状态快照，间隔: {self.snapshot_interval}秒")

            while self.is_running:
                # 等待指定时间
                await asyncio.sleep(self.snapshot_interval)

                if self.is_running:  # 再次检查，避免在sleep期间被停止
                    # 执行快照
                    await self._take_snapshot()

        except asyncio.CancelledError:
            logger.info("快照循环被取消")
            raise
        except Exception as e:
            logger.error(f"快照循环异常: {str(e)}", exc_info=True)
            # 出现异常后尝试重启快照任务
            if self.is_running:
                logger.info("尝试重启快照任务")
                self.snapshot_task = asyncio.create_task(self._snapshot_loop())

    async def _handle_message(self, message: Dict[str, Any]) -> None:
        """处理Redis发布/订阅消息

        Args:
            message: Redis消息
        """
        try:
            # 记录收到的消息类型和频道
            logger.debug(f"收到Redis消息: 类型={message.get('type')}, 频道={message.get('channel')}")

            # 解析消息 - 同时处理message和pmessage类型
            if message["type"] in ["message", "pmessage"]:
                channel = message["channel"]
                logger.info(f"处理频道 {channel} 的消息（类型: {message['type']}）")

                # 处理设备状态变更消息
                if channel == b"device:all:changes" or (isinstance(channel, bytes) and channel.startswith(b"device:") and channel.endswith(b":changes")):
                    try:
                        # 从频道名称中提取Core ID
                        core_id = "default"
                        if channel != b"device:all:changes":
                            # 频道格式为"device:{core_id}:changes"
                            channel_str = channel.decode('utf-8')
                            parts = channel_str.split(':')
                            if len(parts) == 3 and parts[0] == 'device' and parts[2] == 'changes':
                                core_id = parts[1]

                        data = json.loads(message["data"])
                        changes = data.get("changes", [])
                        timestamp = data.get("timestamp", int(time.time()))

                        logger.info(f"收到{len(changes)}个设备状态变更，Core: {core_id}，时间戳: {timestamp}")

                        if changes:
                            # 更新设备状态到数据库
                            for change in changes:
                                try:
                                    device_id = change.get("device_id")
                                    if not device_id:
                                        logger.warning(f"跳过没有device_id的变更: {change}")
                                        continue

                                    # 更新设备状态
                                    new_status = change.get("new_status")
                                    old_status = change.get("old_status", "unknown")
                                    change_timestamp = change.get("timestamp", timestamp)

                                    logger.info(f"设备 {device_id}（Core: {core_id}） 状态变更: {old_status} -> {new_status}")

                                    # 更新MongoDB
                                    await self._update_device_in_db(device_id, new_status, change_timestamp, core_id)

                                    # 通过WebSocket推送状态更新
                                    await self.socketio.emit(
                                        "device_status_update",
                                        {
                                            "id": device_id,
                                            "core_id": core_id,
                                            "status": new_status,
                                            "updated_at": datetime.fromtimestamp(change_timestamp).isoformat()
                                        }
                                    )
                                    logger.debug(f"已通过WebSocket推送设备 {device_id}（Core: {core_id}） 的状态更新")
                                except Exception as e:
                                    logger.error(f"处理设备 {device_id if 'device_id' in locals() else 'unknown'}（Core: {core_id}） 状态变更异常: {str(e)}")
                        else:
                            logger.warning(f"收到的变更列表为空（Core: {core_id}）")
                    except json.JSONDecodeError as e:
                        logger.error(f"解析消息数据JSON失败: {str(e)}")
                        logger.debug(f"原始消息数据: {message.get('data', '')[:100]}...")
                # 处理任务状态消息
                elif isinstance(channel, bytes) and channel.startswith(b"task:") and channel.endswith(b":status"):
                    try:
                        # 从频道名称中提取任务ID
                        # 频道格式为"task:{task_id}:status"
                        channel_str = channel.decode('utf-8')
                        parts = channel_str.split(':')
                        if len(parts) == 3 and parts[0] == 'task' and parts[2] == 'status':
                            task_id = parts[1]

                            # 解析任务状态数据
                            task_data_str = message.get("data", "{}")
                            if isinstance(task_data_str, bytes):
                                task_data_str = task_data_str.decode('utf-8')

                            task_data = json.loads(task_data_str)

                            # 确保task_id字段存在
                            if "task_id" not in task_data:
                                task_data["task_id"] = task_id

                            logger.info(f"收到任务{task_id}的状态更新: {task_data.get('status', 'unknown')}")

                            # 通过WebSocket推送任务状态更新
                            await self.socketio.emit(
                                "task_status_update",
                                task_data,
                                room=f"task_{task_id}"  # 发送到特定任务的房间
                            )
                            logger.debug(f"已通过WebSocket推送任务{task_id}的状态更新")

                            # 同时更新MongoDB中的任务状态
                            try:
                                if self.mongo_db is not None:
                                    # 更新任务状态
                                    update_data = {
                                        "status": task_data.get("status", "unknown"),
                                        "progress": task_data.get("progress", 0),
                                        "updated_at": datetime.now()
                                    }

                                    # 如果任务已完成、失败或取消，添加结束时间
                                    if task_data.get("status") in ["completed", "failed", "canceled"]:
                                        if "end_time" in task_data:
                                            update_data["end_time"] = task_data["end_time"]
                                        else:
                                            update_data["end_time"] = datetime.now().isoformat()

                                    # 如果有开始时间，更新开始时间
                                    if "start_time" in task_data and task_data["start_time"]:
                                        update_data["start_time"] = task_data["start_time"]

                                    # 如果有日志，添加到日志列表
                                    if "logs" in task_data and task_data["logs"]:
                                        try:
                                            # 使用同步方式插入日志
                                            self.mongo_db.social_task_logs.insert_many([
                                                {
                                                    "task_id": task_id,
                                                    "message": log.get("message", ""),
                                                    "level": log.get("level", "info"),
                                                    "timestamp": log.get("timestamp", datetime.now().isoformat()),
                                                    "created_at": datetime.now()
                                                }
                                                for log in task_data["logs"]
                                            ])
                                            logger.debug(f"已插入任务{task_id}的{len(task_data['logs'])}条日志")
                                        except Exception as log_error:
                                            logger.error(f"插入任务{task_id}日志失败: {str(log_error)}")

                                    # 更新任务状态 - 使用同步方式
                                    result = self.mongo_db.social_tasks.update_one(
                                        {"task_id": task_id},
                                        {"$set": update_data}
                                    )

                                    if result.modified_count > 0:
                                        logger.info(f"已更新任务{task_id}的状态到MongoDB: {task_data.get('status')} (进度: {task_data.get('progress', 0)}%)")

                                        # 检查是否需要启动下一个子任务
                                        await self._check_and_start_next_subtask(task_id, task_data)
                                    else:
                                        logger.debug(f"任务{task_id}状态无变化，未更新MongoDB")

                                else:
                                    logger.warning("MongoDB数据库连接不可用，无法更新任务状态")
                            except Exception as e:
                                logger.error(f"更新任务{task_id}状态到MongoDB失败: {str(e)}", exc_info=True)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析任务状态JSON失败: {str(e)}")
                        logger.debug(f"原始消息数据: {message.get('data', '')[:100]}...")
                    except Exception as e:
                        logger.error(f"处理任务状态消息异常: {str(e)}", exc_info=True)
                # 处理其他类型的消息
                else:
                    logger.debug(f"收到未处理的频道消息: {channel}")
            else:
                logger.debug(f"收到非消息类型的数据: {message.get('type')}")

        except Exception as e:
            logger.error(f"处理消息异常: {str(e)}", exc_info=True)

    async def _check_and_start_next_subtask(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """检查并启动下一个子任务

        Args:
            task_id: 当前任务ID
            task_data: 当前任务数据
        """
        try:
            # 只有当子任务完成或失败时才检查
            task_status = task_data.get("status")
            if task_status not in ["completed", "failed"]:
                return

            # 检查当前任务是否是子任务
            current_task = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not current_task or current_task.get("task_type") != "subtask":
                return

            parent_task_id = current_task.get("parent_task_id")
            if not parent_task_id:
                return

            logger.info(f"子任务{task_id}已{task_status}，检查是否需要启动下一个子任务")

            # 查找主任务
            main_task = self.mongo_db.social_tasks.find_one({"task_id": parent_task_id})
            if not main_task:
                logger.warning(f"找不到主任务: {parent_task_id}")
                return

            # 查找所有子任务
            all_subtasks = list(self.mongo_db.social_tasks.find({
                "parent_task_id": parent_task_id,
                "task_type": "subtask"
            }).sort("subtask_index", 1))

            if not all_subtasks:
                logger.warning(f"主任务{parent_task_id}没有子任务")
                return

            # 统计子任务状态
            completed_count = 0
            failed_count = 0
            next_pending_task = None

            for subtask in all_subtasks:
                status = subtask.get("status")
                if status == "completed":
                    completed_count += 1
                elif status == "failed":
                    failed_count += 1
                elif status == "pending" and next_pending_task is None:
                    next_pending_task = subtask

            total_subtasks = len(all_subtasks)
            logger.info(f"主任务{parent_task_id}子任务状态: 总数{total_subtasks}, 完成{completed_count}, 失败{failed_count}")

            # 更新主任务进度
            progress = int((completed_count + failed_count) / total_subtasks * 100)
            main_task_update = {
                "completed_subtasks": completed_count + failed_count,
                "progress": progress,
                "updated_at": datetime.now()
            }

            # 检查是否所有子任务都完成了
            if completed_count + failed_count >= total_subtasks:
                # 所有子任务都完成了
                if completed_count == total_subtasks:
                    # 所有子任务都成功
                    main_task_update["status"] = "completed"
                    main_task_update["end_time"] = datetime.now().isoformat()
                    logger.info(f"主任务{parent_task_id}所有子任务都成功完成")
                else:
                    # 有子任务失败
                    main_task_update["status"] = "failed"
                    main_task_update["end_time"] = datetime.now().isoformat()
                    logger.info(f"主任务{parent_task_id}有{failed_count}个子任务失败")

                # 更新主任务状态
                self.mongo_db.social_tasks.update_one(
                    {"task_id": parent_task_id},
                    {"$set": main_task_update}
                )
                logger.info(f"已更新主任务{parent_task_id}状态为: {main_task_update['status']}")

            elif next_pending_task:
                # 还有待执行的子任务，启动下一个
                next_task_id = next_pending_task["task_id"]
                logger.info(f"启动下一个子任务: {next_task_id}")

                # 更新主任务进度
                self.mongo_db.social_tasks.update_one(
                    {"task_id": parent_task_id},
                    {"$set": main_task_update}
                )

                # 启动下一个子任务
                await self._start_subtask(next_task_id)

            else:
                # 没有更多待执行的子任务，但也没有全部完成（可能有正在运行的）
                logger.info(f"主任务{parent_task_id}没有更多待执行的子任务")

                # 只更新进度
                self.mongo_db.social_tasks.update_one(
                    {"task_id": parent_task_id},
                    {"$set": main_task_update}
                )

        except Exception as e:
            logger.error(f"检查并启动下一个子任务失败: {str(e)}", exc_info=True)

    async def _start_subtask(self, task_id: str) -> None:
        """启动子任务

        Args:
            task_id: 子任务ID
        """
        try:
            # 更新子任务状态为运行中
            self.mongo_db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {
                    "status": "running",
                    "start_time": datetime.now().isoformat(),
                    "updated_at": datetime.now()
                }}
            )

            # 获取子任务信息
            subtask = self.mongo_db.social_tasks.find_one({"task_id": task_id})
            if not subtask:
                logger.error(f"找不到子任务: {task_id}")
                return

            logger.info(f"已更新子任务{task_id}状态为运行中")

            # 通知Core服务启动任务
            # 这里需要调用Core服务的API
            await self._notify_core_service(task_id, subtask)

        except Exception as e:
            logger.error(f"启动子任务{task_id}失败: {str(e)}", exc_info=True)

    async def _notify_core_service(self, task_id: str, task_data: Dict[str, Any]) -> None:
        """通知Core服务启动任务

        Args:
            task_id: 任务ID
            task_data: 任务数据
        """
        try:
            import aiohttp

            # Core服务的API地址（需要从配置获取）
            core_url = "http://localhost:8001"  # 默认Core服务地址

            # 构造请求数据
            request_data = {
                "task_id": task_id,
                "platform_id": task_data.get("platform_id"),
                "account_id": task_data.get("account_id"),
                "device_id": task_data.get("device_id"),
                "content_path": task_data.get("content_path"),
                "metadata": task_data.get("metadata", {}),
                "task_type": task_data.get("task_type", "subtask"),
                "subtask_index": task_data.get("subtask_index"),
                "video_file": task_data.get("video_file")
            }

            logger.info(f"通知Core服务启动任务{task_id}")

            async with aiohttp.ClientSession() as session:
                # 先创建任务
                async with session.post(
                    f"{core_url}/api/tasks",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"Core服务任务{task_id}创建成功")

                        # 然后启动任务
                        async with session.post(
                            f"{core_url}/api/tasks/{task_id}/start",
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as start_response:
                            if start_response.status == 200:
                                logger.info(f"Core服务任务{task_id}启动成功")
                            else:
                                logger.error(f"Core服务任务{task_id}启动失败: {start_response.status}")
                    else:
                        logger.error(f"Core服务任务{task_id}创建失败: {response.status}")

        except Exception as e:
            logger.error(f"通知Core服务启动任务{task_id}失败: {str(e)}", exc_info=True)

    async def _update_device_in_db(self, device_id: str, status: str, timestamp: int, core_id: str = "default") -> None:
        """更新设备状态到数据库

        Args:
            device_id: 设备ID
            status: 设备状态
            timestamp: 时间戳
            core_id: Core服务ID，默认为"default"
        """
        try:
            # 首先更新基本状态
            update_data = {
                "status": status,
                "updated_at": datetime.fromtimestamp(timestamp),
                "core_id": core_id  # 添加Core服务标识
            }

            # 尝试从Redis获取完整的设备信息
            try:
                # 尝试使用新的键名格式（带有Core ID）
                state_json_new = await self.redis_client.get(f"device:{core_id}:{device_id}:state")
                last_change_json_new = await self.redis_client.get(f"device:{core_id}:{device_id}:last_change")

                # 尝试使用旧的键名格式（不带Core ID）
                state_json_old = await self.redis_client.get(f"device:{device_id}:state")
                last_change_json_old = await self.redis_client.get(f"device:{device_id}:last_change")

                # 使用新格式的数据，如果没有则使用旧格式的数据
                state_json = state_json_new if state_json_new else state_json_old
                last_change_json = last_change_json_new if last_change_json_new else last_change_json_old

                if state_json:
                    state = json.loads(state_json)

                    # 添加设备名称和类型
                    if "name" in state:
                        update_data["name"] = state.get("name")

                    # 设备类型 - 注意MongoDB中字段名是"type"
                    if "device_type" in state:
                        update_data["type"] = state.get("device_type")

                    # 添加硬件信息
                    if "hardware_info" in state:
                        hardware_info = state.get("hardware_info", {})
                        # 获取分辨率信息
                        width = hardware_info.get('width', 1080)
                        height = hardware_info.get('height', 1920)

                        # 如果hardware_info中没有分辨率，尝试从display_info获取
                        if "display_info" in state:
                            display_info = state.get("display_info", {})
                            if not width and "width" in display_info:
                                width = display_info.get("width", 1080)
                            if not height and "height" in display_info:
                                height = display_info.get("height", 1920)

                        update_data["config"] = {
                            "resolution": f"{width}x{height}",
                            "cpu_cores": hardware_info.get("cpu_cores", 2),
                            "memory": hardware_info.get("memory_size", 2048),
                            "adb_port": hardware_info.get("adb_port", 5555)
                        }

                    # 添加窗口信息
                    if "window_info" in state:
                        update_data["window_info"] = state.get("window_info", {})

                    # 添加进程信息
                    if "process_info" in state:
                        update_data["process_info"] = state.get("process_info", {})

                    # 添加CPU和内存使用率
                    if "cpu_usage" in state:
                        update_data["cpu_usage"] = state.get("cpu_usage")

                    if "memory_usage" in state:
                        update_data["memory_usage"] = state.get("memory_usage")

                    if "network_status" in state:
                        update_data["network_status"] = state.get("network_status")

                    # 如果有最后一次变更信息，添加到设备数据中
                    if last_change_json:
                        try:
                            last_change = json.loads(last_change_json)
                            update_data["last_change"] = {
                                "old_status": last_change.get("old_status", "unknown"),
                                "new_status": last_change.get("new_status", "unknown"),
                                "timestamp": last_change.get("timestamp", int(time.time()))
                            }
                        except Exception:
                            pass

                    logger.debug(f"从Redis获取到设备{device_id}（Core: {core_id}）的完整信息")
                else:
                    logger.warning(f"设备{device_id}（Core: {core_id}）在Redis中没有状态数据")
            except Exception as e:
                logger.warning(f"从Redis获取设备{device_id}（Core: {core_id}）完整信息失败: {str(e)}")

            # 确保创建时间字段存在
            try:
                # 使用同步方式查询MongoDB
                existing_device = self.mongo_db.devices.find_one({"_id": device_id})
                if not existing_device:
                    update_data["created_at"] = datetime.now()
                elif "created_at" not in existing_device:
                    # 如果设备存在但没有创建时间，添加一个
                    update_data["created_at"] = datetime.now()

                # 记录更新前的字段数量
                before_fields = 0
                if existing_device:
                    before_fields = len(existing_device)
            except Exception as e:
                logger.error(f"查询设备{device_id}异常: {str(e)}")
                # 如果查询失败，默认添加创建时间
                update_data["created_at"] = datetime.now()
                before_fields = 0

            # 记录更新数据的详细内容
            logger.info(f"准备更新设备{device_id}的数据，字段数: {len(update_data)}")
            logger.info(f"更新数据内容: {update_data}")

            # 更新设备状态 - 移除await关键字，因为使用的是同步MongoDB客户端
            try:
                result = self.mongo_db.devices.update_one(
                    {"_id": device_id},
                    {"$set": update_data},
                    upsert=True  # 如果设备不存在则创建
                )

                logger.info(f"MongoDB更新结果: matched={result.matched_count}, modified={result.modified_count}, upserted={result.upserted_id}")

                if result.modified_count > 0 or result.upserted_id:
                    logger.info(f"设备状态已更新: {device_id}, 状态: {status}, 字段数: {len(update_data)}, 之前字段数: {before_fields}")

                    # 验证更新是否成功
                    try:
                        updated_device = self.mongo_db.devices.find_one({"_id": device_id})
                        if updated_device:
                            logger.info(f"更新后的设备数据: {updated_device}")
                            logger.info(f"更新后的字段数: {len(updated_device)}")
                        else:
                            logger.warning(f"无法获取更新后的设备{device_id}数据")
                    except Exception as e:
                        logger.error(f"验证更新结果异常: {str(e)}")
            except Exception as e:
                logger.error(f"MongoDB更新操作异常: {str(e)}", exc_info=True)

                # 记录设备状态历史
                if state_json:
                    try:
                        state = json.loads(state_json)
                        # 异步调用记录状态历史的方法
                        asyncio.create_task(self.history_service.record_status(device_id, state))
                        logger.debug(f"设备{device_id}状态历史记录任务已创建")
                    except Exception as e:
                        logger.warning(f"创建记录设备{device_id}状态历史任务失败: {str(e)}")

        except Exception as e:
            logger.error(f"更新设备状态到数据库异常: {str(e)}", exc_info=True)

    async def _take_snapshot(self) -> None:
        """将Redis中的设备状态保存到MongoDB"""
        try:
            logger.info("开始执行设备状态快照")

            # 检查Redis连接
            try:
                ping_result = await self.redis_client.ping()
                logger.info(f"Redis连接状态: {ping_result}")
            except Exception as e:
                logger.error(f"Redis连接测试失败: {str(e)}")
                return

            # 尝试获取Redis中的所有键，看看是否有设备相关的键
            try:
                # 获取与设备相关的键
                device_keys = await self.redis_client.keys("device:*")
                if device_keys:
                    logger.info(f"Redis中找到{len(device_keys)}个设备相关的键: {device_keys[:5]}...")
                else:
                    logger.warning("Redis中没有找到任何设备相关的键")

                # 获取所有集合
                sets = await self.redis_client.keys("*")
                logger.info(f"Redis中的所有键: {sets[:20]}...")
            except Exception as e:
                logger.error(f"获取Redis键列表失败: {str(e)}")

            # 获取所有Core服务ID
            core_ids = []
            try:
                # 尝试从cores:all集合获取Core服务ID
                core_ids_bytes = await self.redis_client.smembers("cores:all")
                if core_ids_bytes:
                    for core_id in core_ids_bytes:
                        if isinstance(core_id, bytes):
                            core_ids.append(core_id.decode('utf-8'))
                        else:
                            core_ids.append(core_id)
                    logger.info(f"从'cores:all'集合获取到{len(core_ids)}个Core服务ID: {core_ids}")
                else:
                    # 如果没有cores:all集合，使用默认Core ID
                    core_ids = ["default"]
                    logger.info("没有找到Core服务ID，使用默认ID: default")
            except Exception as e:
                logger.error(f"获取Core服务ID列表失败: {str(e)}")
                # 使用默认Core ID
                core_ids = ["default"]
                logger.info("获取Core服务ID异常，使用默认ID: default")

            # 处理每个Core服务的设备
            all_device_ids = []
            for core_id in core_ids:
                try:
                    # 尝试从core:{core_id}:devices集合获取设备ID
                    core_device_ids_bytes = await self.redis_client.smembers(f"core:{core_id}:devices")
                    core_device_ids = []
                    if core_device_ids_bytes:
                        for device_id in core_device_ids_bytes:
                            if isinstance(device_id, bytes):
                                core_device_ids.append(device_id.decode('utf-8'))
                            else:
                                core_device_ids.append(device_id)
                        logger.info(f"从'core:{core_id}:devices'集合获取到{len(core_device_ids)}个设备ID")

                        # 将设备ID和Core ID关联起来
                        for device_id in core_device_ids:
                            all_device_ids.append((device_id, core_id))
                except Exception as e:
                    logger.error(f"获取Core服务{core_id}的设备ID列表失败: {str(e)}")

            # 如果没有从Core服务集合中获取到设备ID，尝试从devices:all集合获取
            if not all_device_ids:
                try:
                    # 从devices:all集合获取设备ID
                    device_ids_bytes = await self.redis_client.smembers("devices:all")
                    if device_ids_bytes:
                        device_ids = []
                        for device_id in device_ids_bytes:
                            if isinstance(device_id, bytes):
                                device_ids.append(device_id.decode('utf-8'))
                            else:
                                device_ids.append(device_id)
                        logger.info(f"从'devices:all'集合获取到{len(device_ids)}个设备ID")

                        # 使用默认Core ID
                        for device_id in device_ids:
                            all_device_ids.append((device_id, "default"))
                except Exception as e:
                    logger.error(f"获取设备ID列表失败: {str(e)}")

            # 如果仍然没有找到设备ID，尝试从键名模式中提取
            if not all_device_ids:
                try:
                    logger.info("没有从集合中找到设备ID，尝试从键名模式中提取")
                    # 获取所有设备状态键
                    state_keys = await self.redis_client.keys("device:*:state")
                    if state_keys:
                        logger.info(f"从键名模式'device:*:state'中找到{len(state_keys)}个键")
                        # 从键名中提取设备ID
                        for key in state_keys:
                            # 键名格式为"device:{id}:state"或"device:{core_id}:{id}:state"
                            key_str = key.decode('utf-8')
                            parts = key_str.split(':')
                            if len(parts) == 3 and parts[0] == 'device' and parts[2] == 'state':
                                device_id = parts[1]
                                all_device_ids.append((device_id, "default"))
                            elif len(parts) == 4 and parts[0] == 'device' and parts[3] == 'state':
                                core_id = parts[1]
                                device_id = parts[2]
                                all_device_ids.append((device_id, core_id))
                        logger.info(f"从键名中提取到{len(all_device_ids)}个设备ID")
                except Exception as e:
                    logger.error(f"从键名提取设备ID失败: {str(e)}")

            if not all_device_ids:
                logger.warning("没有找到设备，快照跳过")
                return

            # 记录找到的设备ID
            logger.info(f"找到{len(all_device_ids)}个设备，准备更新到MongoDB")
            if all_device_ids:
                sample = all_device_ids[:10]
                logger.info(f"设备ID示例: {sample}")

            # 批量获取设备状态
            pipeline = self.redis_client.pipeline()
            for device_id, core_id in all_device_ids:
                # 尝试使用新的键名格式（带有Core ID）
                pipeline.get(f"device:{core_id}:{device_id}:state")
                pipeline.get(f"device:{core_id}:{device_id}:last_change")
                # 同时尝试使用旧的键名格式（不带Core ID）
                pipeline.get(f"device:{device_id}:state")
                pipeline.get(f"device:{device_id}:last_change")

            # 执行管道操作
            results = await pipeline.execute()

            # 更新到MongoDB
            updated_count = 0
            for i in range(0, len(results), 4):
                if i // 4 >= len(all_device_ids):
                    break

                device_id, core_id = all_device_ids[i // 4]

                # 尝试从新的键名格式获取数据
                state_json_new = results[i]
                last_change_json_new = results[i + 1] if i + 1 < len(results) else None

                # 尝试从旧的键名格式获取数据
                state_json_old = results[i + 2] if i + 2 < len(results) else None
                last_change_json_old = results[i + 3] if i + 3 < len(results) else None

                # 使用新格式的数据，如果没有则使用旧格式的数据
                state_json = state_json_new if state_json_new else state_json_old
                last_change_json = last_change_json_new if last_change_json_new else last_change_json_old

                # 记录获取到的数据
                if state_json:
                    logger.debug(f"获取到设备{device_id}（Core: {core_id}）的状态数据")
                else:
                    logger.warning(f"设备{device_id}（Core: {core_id}）没有状态数据")

                if last_change_json:
                    logger.debug(f"获取到设备{device_id}（Core: {core_id}）的最后一次变更数据")
                else:
                    logger.debug(f"设备{device_id}（Core: {core_id}）没有最后一次变更数据")

                if state_json:
                    try:
                        state = json.loads(state_json)
                        # 更新设备状态和基本信息
                        device_data = {
                            "status": state.get("status", "unknown"),
                            "updated_at": datetime.fromtimestamp(state.get("last_sync", int(time.time()))),
                            "core_id": core_id  # 添加Core服务标识
                        }

                        # 添加设备名称和类型
                        if "name" in state:
                            device_data["name"] = state.get("name")

                        # 设备类型 - 注意MongoDB中字段名是"type"
                        if "device_type" in state:
                            device_data["type"] = state.get("device_type")

                        # 从状态中获取硬件信息
                        hardware_info = {}
                        if "hardware_info" in state:
                            hardware_info = state.get("hardware_info", {})
                            logger.debug(f"从状态中获取到设备{device_id}（Core: {core_id}）的硬件信息")

                        # 获取分辨率信息
                        width = hardware_info.get('width', 1080)
                        height = hardware_info.get('height', 1920)

                        # 如果hardware_info中没有分辨率，尝试从display_info获取
                        if "display_info" in state:
                            display_info = state.get("display_info", {})
                            if not width and "width" in display_info:
                                width = display_info.get("width", 1080)
                            if not height and "height" in display_info:
                                height = display_info.get("height", 1920)

                        device_data["config"] = {
                            "resolution": f"{width}x{height}",
                            "cpu_cores": hardware_info.get("cpu_cores", 2),
                            "memory": hardware_info.get("memory_size", 2048),
                            "adb_port": hardware_info.get("adb_port", 5555)
                        }

                        # 添加窗口信息
                        if "window_info" in state:
                            window_info = state.get("window_info", {})
                            device_data["window_info"] = window_info

                        # 添加进程信息
                        if "process_info" in state:
                            process_info = state.get("process_info", {})
                            device_data["process_info"] = process_info

                        # 添加CPU和内存使用率
                        if "cpu_usage" in state:
                            device_data["cpu_usage"] = state.get("cpu_usage")

                        if "memory_usage" in state:
                            device_data["memory_usage"] = state.get("memory_usage")

                        if "network_status" in state:
                            device_data["network_status"] = state.get("network_status")

                        # 如果有最后一次变更信息，添加到设备数据中
                        if last_change_json:
                            try:
                                last_change = json.loads(last_change_json)
                                device_data["last_change"] = {
                                    "old_status": last_change.get("old_status", "unknown"),
                                    "new_status": last_change.get("new_status", "unknown"),
                                    "timestamp": last_change.get("timestamp", int(time.time()))
                                }
                                logger.debug(f"添加设备{device_id}（Core: {core_id}）的最后一次变更信息")
                            except json.JSONDecodeError:
                                logger.warning(f"解析设备{device_id}（Core: {core_id}）的最后一次变更JSON失败")
                            except Exception as e:
                                logger.warning(f"处理设备{device_id}（Core: {core_id}）的最后一次变更异常: {str(e)}")

                        # 确保创建时间字段存在
                        try:
                            # 使用同步方式查询MongoDB
                            existing_device = self.mongo_db.devices.find_one({"_id": device_id})
                            if not existing_device:
                                device_data["created_at"] = datetime.now()
                            elif "created_at" not in existing_device:
                                # 如果设备存在但没有创建时间，添加一个
                                device_data["created_at"] = datetime.now()

                            # 记录快照前的字段数量
                            before_fields = 0
                            if existing_device:
                                before_fields = len(existing_device)
                        except Exception as e:
                            logger.error(f"快照：查询设备{device_id}异常: {str(e)}")
                            # 如果查询失败，默认添加创建时间
                            device_data["created_at"] = datetime.now()
                            before_fields = 0

                        # 记录更新数据的详细内容
                        logger.info(f"快照：准备更新设备{device_id}的数据，字段数: {len(device_data)}")
                        logger.info(f"快照：更新数据内容: {device_data}")

                        # 更新到MongoDB
                        try:
                            result = self.mongo_db.devices.update_one(
                                {"_id": device_id},
                                {"$set": device_data},
                                upsert=True  # 如果设备不存在则创建
                            )

                            logger.info(f"快照：MongoDB更新结果: matched={result.matched_count}, modified={result.modified_count}, upserted={result.upserted_id}")

                            if result.modified_count > 0 or result.upserted_id:
                                logger.info(f"快照：设备信息已更新: {device_id}, 字段数: {len(device_data)}, 之前字段数: {before_fields}")

                                # 验证更新是否成功
                                try:
                                    updated_device = self.mongo_db.devices.find_one({"_id": device_id})
                                    if updated_device:
                                        logger.info(f"快照：更新后的设备数据: {updated_device}")
                                        logger.info(f"快照：更新后的字段数: {len(updated_device)}")
                                    else:
                                        logger.warning(f"快照：无法获取更新后的设备{device_id}数据")
                                except Exception as e:
                                    logger.error(f"快照：验证更新结果异常: {str(e)}")

                                # 记录设备状态历史
                                # 异步调用记录状态历史的方法
                                asyncio.create_task(self.history_service.record_status(device_id, state))
                                logger.debug(f"设备{device_id}状态历史记录任务已创建")
                        except Exception as e:
                            logger.error(f"快照：MongoDB更新操作异常: {str(e)}", exc_info=True)

                        updated_count += 1
                    except json.JSONDecodeError as e:
                        logger.error(f"解析设备{device_id}状态JSON失败: {str(e)}")
                    except Exception as e:
                        logger.error(f"处理设备{device_id}状态异常: {str(e)}", exc_info=True)
                else:
                    logger.warning(f"设备{device_id}在Redis中没有状态数据")

            logger.info(f"设备状态快照完成，共{updated_count}个设备")

        except Exception as e:
            logger.error(f"执行设备状态快照异常: {str(e)}", exc_info=True)
