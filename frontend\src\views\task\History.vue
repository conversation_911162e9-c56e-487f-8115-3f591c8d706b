<template>
  <div class="task-history">
    <div class="history-header">
      <h1>📋 执行历史</h1>
      <p class="header-description">查看所有任务的执行历史记录</p>
    </div>

    <div class="history-content">
      <!-- 筛选条件 -->
      <el-card class="filter-card">
        <template #header>
          <span>🔍 筛选条件</span>
        </template>

        <el-form :model="filterForm" inline>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="选择状态" clearable style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="已取消" value="canceled" />
            </el-select>
          </el-form-item>

          <el-form-item label="平台">
            <el-select v-model="filterForm.platform_id" placeholder="选择平台" clearable style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="YouTube" value="youtube" />
              <el-option label="TikTok" value="tiktok" />
              <el-option label="Instagram" value="instagram" />
            </el-select>
          </el-form-item>

          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="searchHistory" :loading="loading">
              搜索
            </el-button>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="danger" @click="showCleanDialog">清理历史</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 历史记录列表 -->
      <el-card class="history-list-card">
        <template #header>
          <div class="card-header">
            <span>📝 历史记录</span>
            <div class="header-actions">
              <el-button type="success" size="small" @click="exportHistory">
                导出数据
              </el-button>
              <el-button type="primary" size="small" @click="refreshHistory" :loading="loading">
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table :data="historyList" v-loading="loading" stripe>
          <el-table-column prop="id" label="任务ID" width="120" show-overflow-tooltip />
          <el-table-column prop="platform_name" label="平台" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.platform_name || getPlatformName(row.platform_id) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="account_name" label="账号" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span :title="row.account_name || row.account_id">{{ row.account_name || row.account_id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="100">
            <template #default="{ row }">
              <span>{{ row.progress || 0 }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="start_time" label="开始时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="end_time" label="结束时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.end_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100">
            <template #default="{ row }">
              {{ calculateDuration(row.start_time, row.end_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="info"
                size="small"
                @click="viewTaskDetails(row)"
              >
                详情
              </el-button>
              <el-button
                v-if="row.status === 'completed'"
                type="success"
                size="small"
                @click="viewTaskResult(row)"
              >
                结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="任务详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="平台">{{ selectedTask.platform_name || getPlatformName(selectedTask.platform_id) }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ selectedTask.account_name || selectedTask.account_id }}</el-descriptions-item>
          <el-descriptions-item label="内容路径" :span="2">{{ selectedTask.content_path }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedTask.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(selectedTask.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatTime(selectedTask.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="执行耗时">{{ calculateDuration(selectedTask.start_time, selectedTask.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="进度" :span="2">
            <el-progress
              :percentage="selectedTask.progress || 0"
              :status="getProgressStatus(selectedTask.status)"
            />
          </el-descriptions-item>
        </el-descriptions>

        <!-- 任务日志 -->
        <div class="task-logs" style="margin-top: 20px;">
          <h4>执行日志</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in taskLogs"
              :key="index"
              :timestamp="log.timestamp"
              :type="getLogType(log.level)"
              size="small"
            >
              {{ log.message }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="selectedTask && selectedTask.status === 'completed'"
          type="primary"
          @click="viewTaskResult(selectedTask)"
        >
          查看结果
        </el-button>
      </template>
    </el-dialog>

    <!-- 清理历史对话框 -->
    <el-dialog
      v-model="cleanDialogVisible"
      title="清理历史记录"
      width="500px"
    >
      <el-form :model="cleanForm" label-width="120px">
        <el-form-item label="清理条件">
          <el-radio-group v-model="cleanForm.type">
            <el-radio label="date">按日期清理</el-radio>
            <el-radio label="count">保留最新记录</el-radio>
            <el-radio label="status">按状态清理</el-radio>
            <el-radio label="invalid">清理无效任务</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="cleanForm.type === 'date'" label="清理日期前">
          <el-date-picker
            v-model="cleanForm.beforeDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item v-if="cleanForm.type === 'count'" label="保留数量">
          <el-input-number v-model="cleanForm.keepCount" :min="1" :max="1000" />
        </el-form-item>

        <el-form-item v-if="cleanForm.type === 'status'" label="清理状态">
          <el-select v-model="cleanForm.status" placeholder="选择要清理的状态">
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
            <el-option label="已取消" value="canceled" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="cleanForm.type === 'invalid'" label="说明">
          <el-alert
            title="将清理没有任务ID的无效任务记录"
            type="warning"
            :closable="false"
            show-icon
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cleanDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmClean" :loading="cleanLoading">
          确认清理
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTaskHistory, cleanTaskHistory, exportTasks, getTaskLogs } from '@/api/task'

// 响应式数据
const loading = ref(false)
const cleanLoading = ref(false)
const historyList = ref([])
const detailDialogVisible = ref(false)
const cleanDialogVisible = ref(false)
const selectedTask = ref(null)
const taskLogs = ref([])

// 筛选表单
const filterForm = reactive({
  status: '',
  platform_id: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 清理表单
const cleanForm = reactive({
  type: 'date',
  beforeDate: '',
  keepCount: 100,
  status: ''
})

// 初始化
onMounted(() => {
  fetchHistory()
})

// 获取历史记录
const fetchHistory = async () => {
  try {
    loading.value = true

    const params = {
      limit: pagination.size,
      offset: (pagination.current - 1) * pagination.size,
      status: filterForm.status || undefined,
      platform_id: filterForm.platform_id || undefined,
      start_date: filterForm.dateRange?.[0] || undefined,
      end_date: filterForm.dateRange?.[1] || undefined
    }

    console.log('获取历史记录参数:', params)

    // 调用真实的API获取历史记录
    const response = await getTaskHistory(params)
    console.log('历史记录响应:', response)

    if (response && response.data) {
      historyList.value = response.data.tasks || []
      pagination.total = response.data.total || 0
      console.log('历史记录数据:', historyList.value)
    } else {
      console.warn('API响应数据格式异常，使用空数组')
      historyList.value = []
      pagination.total = 0
    }

  } catch (error) {
    console.error('获取历史记录失败:', error)
    ElMessage.warning('历史记录功能开发中，暂时显示空数据')
    historyList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索历史
const searchHistory = () => {
  pagination.current = 1
  fetchHistory()
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    platform_id: '',
    dateRange: []
  })
  pagination.current = 1
  fetchHistory()
}

// 刷新历史
const refreshHistory = () => {
  fetchHistory()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  fetchHistory()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchHistory()
}

// 查看任务详情
const viewTaskDetails = async (task) => {
  selectedTask.value = task
  detailDialogVisible.value = true

  // 获取任务日志
  try {
    // 模拟日志数据
    taskLogs.value = [
      {
        message: '任务开始执行',
        level: 'info',
        timestamp: task.start_time
      },
      {
        message: '正在处理视频文件',
        level: 'info',
        timestamp: new Date(new Date(task.start_time).getTime() + 30000).toISOString()
      },
      {
        message: task.status === 'completed' ? '任务执行完成' : '任务执行失败',
        level: task.status === 'completed' ? 'success' : 'error',
        timestamp: task.end_time
      }
    ]
  } catch (error) {
    console.error('获取任务日志失败:', error)
    taskLogs.value = []
  }
}

// 查看任务结果
const viewTaskResult = (task) => {
  // 跳转到通用的任务结果页面
  window.open(`/tasks/result?taskId=${task.id}`, '_blank')
}

// 显示清理对话框
const showCleanDialog = () => {
  cleanDialogVisible.value = true
}

// 确认清理
const confirmClean = async () => {
  try {
    await ElMessageBox.confirm('确定要清理历史记录吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    cleanLoading.value = true

    const params = {}
    if (cleanForm.type === 'date') {
      params.before_date = cleanForm.beforeDate
    } else if (cleanForm.type === 'count') {
      params.keep_count = cleanForm.keepCount
    } else if (cleanForm.type === 'status') {
      params.status = cleanForm.status
    } else if (cleanForm.type === 'invalid') {
      params.clean_invalid = true
    }

    await cleanTaskHistory(params)

    ElMessage.success('历史记录清理完成')
    cleanDialogVisible.value = false
    await fetchHistory()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理历史记录失败:', error)
      ElMessage.error('清理历史记录失败')
    }
  } finally {
    cleanLoading.value = false
  }
}

// 导出历史
const exportHistory = async () => {
  try {
    ElMessage.info('导出功能开发中')
    // const params = {
    //   start_date: filterForm.dateRange?.[0],
    //   end_date: filterForm.dateRange?.[1],
    //   status: filterForm.status,
    //   format: 'excel'
    // }
    // await exportTasks(params)
  } catch (error) {
    console.error('导出历史记录失败:', error)
    ElMessage.error('导出历史记录失败')
  }
}

// 工具函数
const getPlatformName = (platformId) => {
  const platforms = {
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'instagram': 'Instagram'
  }
  return platforms[platformId] || platformId
}

const getStatusType = (status) => {
  const types = {
    'completed': 'success',
    'failed': 'danger',
    'canceled': 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'completed': '已完成',
    'failed': '失败',
    'canceled': '已取消'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
}

const getLogType = (level) => {
  const types = {
    'success': 'success',
    'warning': 'warning',
    'error': 'danger',
    'info': 'primary'
  }
  return types[level] || 'primary'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString()
}

const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '-'

  const start = new Date(startTime)
  const end = new Date(endTime)
  const duration = end.getTime() - start.getTime()

  const minutes = Math.floor(duration / 60000)
  const seconds = Math.floor((duration % 60000) / 1000)

  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
}
</script>

<style scoped>
.task-history {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.history-header {
  margin-bottom: 20px;
}

.history-header h1 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.8rem;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.history-list-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #409EFF;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.task-detail {
  max-height: 500px;
  overflow-y: auto;
}

.task-logs {
  max-height: 300px;
  overflow-y: auto;
}

.task-logs h4 {
  margin: 0 0 15px 0;
  color: #409EFF;
}
</style>